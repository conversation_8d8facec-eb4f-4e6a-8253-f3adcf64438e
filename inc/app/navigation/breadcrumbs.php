<?php
/**
 * Breadcrumbs Navigation - Modernized with Creative Design
 * @package HalaCoupon
 * @subpackage App\Navigation
 * @version 2.0.0 - Modernized
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Helper function to render RTL/LTR aware separator
 */
function halacoupon_breadcrumb_separator() {
    echo '<li class="flex items-center mx-2" aria-hidden="true">';
    if (is_rtl()) {
        echo '<svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
        echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>';
        echo '</svg>';
    } else {
        echo '<svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
        echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>';
        echo '</svg>';
    }
    echo '</li>';
}

/**
 * Display modern breadcrumbs with creative design and structured data
 *
 * Enhanced version - Added support for Review post type and archive cases
 * Supports: Store pages, Category pages, Blog posts, Review articles, Static pages, Search, 404
 * Features: RTL/LTR support, Simple gray design, Creative shadows, Accessibility
 */
function halacoupon_breadcrumbs() {
    if (is_front_page() || is_home()) {
        return; // Don't show breadcrumbs on the front page or blog home.
    }

    $all_stores_page_id = halacoupon_get_option('all_stores_page');
    $all_categories_page_id = halacoupon_get_option('all_categories_page');

    $breadcrumbs = [];
    $position = 1;

    // Simple elegant breadcrumb container with creative shadows
    echo '<div class="relative">';
    echo '<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">';
    echo '<div class="bg-gray-50 p-4 relative overflow-hidden">';

    // Creative shadow elements
    echo '<div class="absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-gray-200/30 to-transparent rounded-full -translate-x-16 -translate-y-16"></div>';
    echo '<div class="absolute bottom-0 right-0 w-24 h-24 bg-gradient-to-tl from-gray-300/20 to-transparent rounded-full translate-x-12 translate-y-12"></div>';

    echo '<nav class="relative flex items-center" aria-label="' . esc_attr__('Breadcrumb Navigation', 'halacoupon') . '">';
    echo '<ol class="flex items-center text-sm" itemscope itemtype="https://schema.org/BreadcrumbList">';

    // Home link with simple elegant design
    echo '<li class="flex items-center" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
    echo '<a href="' . esc_url(home_url('/')) . '" class="group flex items-center gap-2 px-3 py-2 bg-white rounded-lg shadow-soft border border-gray-200 hover:shadow-medium hover:bg-gray-50 hover:border-gray-300 transition-all duration-300" itemprop="item">';
    echo '<svg class="w-4 h-4 text-gray-600 group-hover:text-gray-800 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
    echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>';
    echo '</svg>';
    echo '<span class="font-medium text-gray-700 group-hover:text-gray-900 transition-colors duration-300" itemprop="name">' . esc_html__('Home', 'halacoupon') . '</span>';
    echo '<meta itemprop="position" content="' . $position . '">';
    echo '</a>';
    echo '</li>';

    // RTL/LTR aware separator
    halacoupon_breadcrumb_separator();

    $breadcrumbs[] = [
        '@type' => 'ListItem',
        'position' => $position++,
        'name' => esc_html__('Home', 'halacoupon'),
        'item' => esc_url(home_url('/')),
    ];

    // Check different page types and build breadcrumbs accordingly
    if (is_tax('coupon_store')) {
        $all_stores_url = $all_stores_page_id ? get_permalink($all_stores_page_id) : '';
        $all_stores_title = $all_stores_page_id ? get_the_title($all_stores_page_id) : esc_html__('All Stores', 'halacoupon');

        if ($all_stores_url) {
            echo '<li class="flex items-center" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
            echo '<a href="' . esc_url($all_stores_url) . '" class="group flex items-center gap-2 px-3 py-2 bg-white rounded-lg shadow-soft border border-gray-200 hover:shadow-medium hover:bg-gray-50 hover:border-gray-300 transition-all duration-300" itemprop="item">';
            echo '<svg class="w-4 h-4 text-gray-600 group-hover:text-gray-800 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>';
            echo '</svg>';
            echo '<span class="font-medium text-gray-700 group-hover:text-gray-900 transition-colors duration-300" itemprop="name">' . esc_html($all_stores_title) . '</span>';
            echo '<meta itemprop="position" content="' . $position . '">';
            echo '</a>';
            echo '</li>';

            halacoupon_breadcrumb_separator();

            $breadcrumbs[] = [
                '@type' => 'ListItem',
                'position' => $position++,
                'name' => esc_html($all_stores_title),
                'item' => esc_url($all_stores_url),
            ];
        }

        // Current store (active state)
        echo '<li class="flex items-center" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
        echo '<div class="flex items-center gap-2 px-4 py-2 btn-outline text-black rounded-lg shadow-medium border border-gray-700 relative">';
        echo '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
        echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>';
        echo '</svg>';
        echo '<span class="font-bold" itemprop="name">' . single_term_title('', false) . '</span>';
        echo '<meta itemprop="position" content="' . $position . '">';
        echo '</div>';
        echo '</li>';

        $breadcrumbs[] = [
            '@type' => 'ListItem',
            'position' => $position++,
            'name' => single_term_title('', false),
        ];
    } elseif (is_tax('coupon_category')) {
        $all_categories_url = $all_categories_page_id ? get_permalink($all_categories_page_id) : '';
        $all_categories_title = $all_categories_page_id ? get_the_title($all_categories_page_id) : esc_html__('All Categories', 'halacoupon');

        if ($all_categories_url) {
            echo '<li class="flex items-center" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
            echo '<a href="' . esc_url($all_categories_url) . '" class="group flex items-center gap-2 px-3 py-2 bg-white rounded-lg shadow-soft border border-gray-200 hover:shadow-medium hover:bg-gray-50 hover:border-gray-300 transition-all duration-300" itemprop="item">';
            echo '<svg class="w-4 h-4 text-gray-600 group-hover:text-gray-800 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>';
            echo '</svg>';
            echo '<span class="font-medium text-gray-700 group-hover:text-gray-900 transition-colors duration-300" itemprop="name">' . esc_html($all_categories_title) . '</span>';
            echo '<meta itemprop="position" content="' . $position . '">';
            echo '</a>';
            echo '</li>';

            halacoupon_breadcrumb_separator();

            $breadcrumbs[] = [
                '@type' => 'ListItem',
                'position' => $position++,
                'name' => esc_html($all_categories_title),
                'item' => esc_url($all_categories_url),
            ];
        }

        // Current category (active state)
        echo '<li class="flex items-center" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
        echo '<div class="flex items-center gap-2 px-4 py-2 btn-outline text-black rounded-lg shadow-medium border border-gray-700 relative">';
        echo '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
        echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>';
        echo '</svg>';
        echo '<span class="font-bold" itemprop="name">' . single_term_title('', false) . '</span>';
        echo '<meta itemprop="position" content="' . $position . '">';
        echo '</div>';
        echo '</li>';

        $breadcrumbs[] = [
            '@type' => 'ListItem',
            'position' => $position++,
            'name' => single_term_title('', false),
        ];
    } elseif (is_post_type_archive('review')) {
        // Review Articles Archive page
        echo '<li class="flex items-center" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
        echo '<div class="flex items-center gap-2 px-4 py-2 btn-outline text-black rounded-lg shadow-medium border border-gray-700 relative">';
        echo '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
        echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>';
        echo '</svg>';
        echo '<span class="font-bold" itemprop="name">' . esc_html__('Review Articles', 'halacoupon') . '</span>';
        echo '<meta itemprop="position" content="' . $position . '">';
        echo '</div>';
        echo '</li>';

        $breadcrumbs[] = [
            '@type' => 'ListItem',
            'position' => $position++,
            'name' => esc_html__('Review Articles', 'halacoupon'),
        ];
    } elseif (is_tax('review_category')) {
        // Review Category taxonomy page
        echo '<li class="flex items-center" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
        echo '<a href="' . esc_url(get_post_type_archive_link('review')) . '" class="group flex items-center gap-2 px-3 py-2 bg-white rounded-lg shadow-soft border border-gray-200 hover:shadow-medium hover:bg-gray-50 hover:border-gray-300 transition-all duration-300" itemprop="item">';
        echo '<svg class="w-4 h-4 text-gray-600 group-hover:text-gray-800 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
        echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>';
        echo '</svg>';
        echo '<span class="font-medium text-gray-700 group-hover:text-gray-900 transition-colors duration-300" itemprop="name">' . esc_html__('Review Articles', 'halacoupon') . '</span>';
        echo '<meta itemprop="position" content="' . $position . '">';
        echo '</a>';
        echo '</li>';

        halacoupon_breadcrumb_separator();

        $breadcrumbs[] = [
            '@type' => 'ListItem',
            'position' => $position++,
            'name' => esc_html__('Review Articles', 'halacoupon'),
            'item' => esc_url(get_post_type_archive_link('review')),
        ];

        // Handle hierarchical review categories (parent categories)
        $current_term = get_queried_object();
        $parent_terms = array();
        
        if ($current_term->parent) {
            $parent = get_term($current_term->parent, 'review_category');
            while ($parent && !is_wp_error($parent)) {
                array_unshift($parent_terms, $parent);
                if ($parent->parent) {
                    $parent = get_term($parent->parent, 'review_category');
                } else {
                    break;
                }
            }
        }

        // Display parent categories
        foreach ($parent_terms as $parent_term) {
            echo '<li class="flex items-center" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
            echo '<a href="' . esc_url(get_term_link($parent_term)) . '" class="group flex items-center gap-2 px-3 py-2 bg-white rounded-lg shadow-soft border border-gray-200 hover:shadow-medium hover:bg-gray-50 hover:border-gray-300 transition-all duration-300" itemprop="item">';
            echo '<svg class="w-4 h-4 text-gray-600 group-hover:text-gray-800 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>';
            echo '</svg>';
            echo '<span class="font-medium text-gray-700 group-hover:text-gray-900 transition-colors duration-300" itemprop="name">' . esc_html($parent_term->name) . '</span>';
            echo '<meta itemprop="position" content="' . $position . '">';
            echo '</a>';
            echo '</li>';

            halacoupon_breadcrumb_separator();

            $breadcrumbs[] = [
                '@type' => 'ListItem',
                'position' => $position++,
                'name' => esc_html($parent_term->name),
                'item' => esc_url(get_term_link($parent_term)),
            ];
        }

        // Current review category (active state)
        echo '<li class="flex items-center" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
        echo '<div class="flex items-center gap-2 px-4 py-2 btn-outline text-black rounded-lg shadow-medium border border-gray-700 relative">';
        echo '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
        echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>';
        echo '</svg>';
        echo '<span class="font-bold" itemprop="name">' . single_term_title('', false) . '</span>';
        echo '<meta itemprop="position" content="' . $position . '">';
        echo '</div>';
        echo '</li>';

        $breadcrumbs[] = [
            '@type' => 'ListItem',
            'position' => $position++,
            'name' => single_term_title('', false),
        ];
    } elseif (is_singular('review')) {
        // Single Review Article page
        echo '<li class="flex items-center" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
        echo '<a href="' . esc_url(get_post_type_archive_link('review')) . '" class="group flex items-center gap-2 px-3 py-2 bg-white rounded-lg shadow-soft border border-gray-200 hover:shadow-medium hover:bg-gray-50 hover:border-gray-300 transition-all duration-300" itemprop="item">';
        echo '<svg class="w-4 h-4 text-gray-600 group-hover:text-gray-800 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
        echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>';
        echo '</svg>';
        echo '<span class="font-medium text-gray-700 group-hover:text-gray-900 transition-colors duration-300" itemprop="name">' . esc_html__('Review Articles', 'halacoupon') . '</span>';
        echo '<meta itemprop="position" content="' . $position . '">';
        echo '</a>';
        echo '</li>';

        halacoupon_breadcrumb_separator();

        $breadcrumbs[] = [
            '@type' => 'ListItem',
            'position' => $position++,
            'name' => esc_html__('Review Articles', 'halacoupon'),
            'item' => esc_url(get_post_type_archive_link('review')),
        ];

        // Check if review has category and add it
        $review_categories = get_the_terms(get_the_ID(), 'review_category');
        if ($review_categories && !is_wp_error($review_categories)) {
            $category = $review_categories[0]; // Get first category
            echo '<li class="flex items-center" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
            echo '<a href="' . esc_url(get_term_link($category)) . '" class="group flex items-center gap-2 px-3 py-2 bg-white rounded-lg shadow-soft border border-gray-200 hover:shadow-medium hover:bg-gray-50 hover:border-gray-300 transition-all duration-300" itemprop="item">';
            echo '<svg class="w-4 h-4 text-gray-600 group-hover:text-gray-800 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>';
            echo '</svg>';
            echo '<span class="font-medium text-gray-700 group-hover:text-gray-900 transition-colors duration-300" itemprop="name">' . esc_html($category->name) . '</span>';
            echo '<meta itemprop="position" content="' . $position . '">';
            echo '</a>';
            echo '</li>';

            halacoupon_breadcrumb_separator();

            $breadcrumbs[] = [
                '@type' => 'ListItem',
                'position' => $position++,
                'name' => esc_html($category->name),
                'item' => esc_url(get_term_link($category)),
            ];
        }

        // Current review article (active state)
        echo '<li class="flex items-center" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
        echo '<div class="flex items-center gap-2 px-4 py-2 bg-gray-800 btn-outline text-black rounded-lg shadow-medium border border-gray-700 relative">';
        echo '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
        echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>';
        echo '</svg>';
        echo '<span class="font-bold" itemprop="name">' . get_the_title() . '</span>';
        echo '<meta itemprop="position" content="' . $position . '">';
        echo '</div>';
        echo '</li>';

        $breadcrumbs[] = [
            '@type' => 'ListItem',
            'position' => $position++,
            'name' => get_the_title(),
        ];
    } elseif (is_single() && get_post_type() == 'post') {
        $blog_page_url = get_permalink(get_option('page_for_posts'));
        if ($blog_page_url) {
            echo '<li class="flex items-center" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
            echo '<a href="' . esc_url($blog_page_url) . '" class="group flex items-center gap-2 px-3 py-2 bg-white rounded-lg shadow-soft border border-gray-200 hover:shadow-medium hover:bg-gray-50 hover:border-gray-300 transition-all duration-300" itemprop="item">';
            echo '<svg class="w-4 h-4 text-gray-600 group-hover:text-gray-800 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>';
            echo '</svg>';
            echo '<span class="font-medium text-gray-700 group-hover:text-gray-900 transition-colors duration-300" itemprop="name">' . esc_html__('Blog', 'halacoupon') . '</span>';
            echo '<meta itemprop="position" content="' . $position . '">';
            echo '</a>';
            echo '</li>';

            halacoupon_breadcrumb_separator();

            $breadcrumbs[] = [
                '@type' => 'ListItem',
                'position' => $position++,
                'name' => esc_html__('Blog', 'halacoupon'),
                'item' => esc_url($blog_page_url),
            ];
        }

        // Current blog post (active state)
        echo '<li class="flex items-center" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
        echo '<div class="flex items-center gap-2 px-4 py-2 btn-outline text-black rounded-lg shadow-medium border border-gray-700 relative">';
        echo '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
        echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>';
        echo '</svg>';
        echo '<span class="font-bold" itemprop="name">' . get_the_title() . '</span>';
        echo '<meta itemprop="position" content="' . $position . '">';
        echo '</div>';
        echo '</li>';

        $breadcrumbs[] = [
            '@type' => 'ListItem',
            'position' => $position++,
            'name' => get_the_title(),
        ];

    } elseif (is_page()) {
        // Current page (active state)
        echo '<li class="flex items-center" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
        echo '<div class="flex items-center gap-2 px-4 py-2 btn-outline text-black rounded-lg shadow-medium border border-gray-700 relative">';
        echo '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
        echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>';
        echo '</svg>';
        echo '<span class="font-bold" itemprop="name">' . get_the_title() . '</span>';
        echo '<meta itemprop="position" content="' . $position . '">';
        echo '</div>';
        echo '</li>';

        $breadcrumbs[] = [
            '@type' => 'ListItem',
            'position' => $position++,
            'name' => get_the_title(),
        ];

    } elseif (is_search()) {
        // Search results page
        echo '<li class="flex items-center" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
        echo '<div class="flex items-center gap-2 px-4 py-2 btn-outline text-black rounded-lg shadow-medium border border-gray-700 relative">';
        echo '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
        echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>';
        echo '</svg>';
        echo '<span class="font-bold" itemprop="name">' . sprintf(esc_html__('Search Results for: %s', 'halacoupon'), get_search_query()) . '</span>';
        echo '<meta itemprop="position" content="' . $position . '">';
        echo '</div>';
        echo '</li>';

        $breadcrumbs[] = [
            '@type' => 'ListItem',
            'position' => $position++,
            'name' => sprintf(esc_html__('Search Results for: %s', 'halacoupon'), get_search_query()),
        ];

    } elseif (is_404()) {
        // 404 error page
        echo '<li class="flex items-center" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
        echo '<div class="flex items-center gap-2 px-4 py-2 btn-outline text-black rounded-lg shadow-medium border border-gray-700 relative">';
        echo '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
        echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>';
        echo '</svg>';
        echo '<span class="font-bold" itemprop="name">' . esc_html__('Page Not Found', 'halacoupon') . '</span>';
        echo '<meta itemprop="position" content="' . $position . '">';
        echo '</div>';
        echo '</li>';

        $breadcrumbs[] = [
            '@type' => 'ListItem',
            'position' => $position++,
            'name' => esc_html__('Page Not Found', 'halacoupon'),
        ];

    } elseif (is_category() || is_tag() || is_tax()) {
        $taxonomy = get_queried_object();

        // Current taxonomy term (active state)
        echo '<li class="flex items-center" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
        echo '<div class="flex items-center gap-2 px-4 py-2 btn-outline text-black rounded-lg shadow-medium border border-gray-700 relative">';
        echo '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
        echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>';
        echo '</svg>';
        echo '<span class="font-bold" itemprop="name">' . esc_html($taxonomy->name) . '</span>';
        echo '<meta itemprop="position" content="' . $position . '">';
        echo '</div>';
        echo '</li>';

        $breadcrumbs[] = [
            '@type' => 'ListItem',
            'position' => $position++,
            'name' => esc_html($taxonomy->name),
        ];
    }

    echo '</ol>'; // Close breadcrumb list
    echo '</nav>'; // Close navigation
    echo '</div>'; // Close bg container
    echo '</div>'; // Close max-w container
    echo '</div>'; // Close main container

    // Enhanced structured data with additional properties
    $breadcrumb_schema = [
        '@context' => 'https://schema.org',
        '@type' => 'BreadcrumbList',
        'itemListElement' => $breadcrumbs,
        'name' => esc_html__('Breadcrumb Navigation', 'halacoupon'),
        'description' => esc_html__('Navigate through the site hierarchy', 'halacoupon'),
    ];

    echo '<script type="application/ld+json">' . wp_json_encode($breadcrumb_schema, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . '</script>';
}

// Mark this module as loaded
add_filter('halacoupon_module_breadcrumbs_loaded', '__return_true');
add_filter('halacoupon_loaded_modules', function($modules) {
    $modules[] = 'navigation/breadcrumbs';
    return $modules;
});
