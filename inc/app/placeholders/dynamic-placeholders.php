<?php
/**
 * Dynamic Placeholders System - Production Ready
 *
 * This system provides dynamic placeholder replacement for content throughout the website.
 * All placeholders are processed server-side for optimal SEO performance.
 *
 * FEATURES:
 * - 16 Essential placeholders for coupon/deal websites
 * - Context-aware store/category/coupon detection
 * - Server-side processing for SEO optimization
 * - Redux theme options integration
 * - Live testing interface
 * - Multisite support
 * - Performance optimized with caching
 *
 * USAGE:
 * Use {placeholder} format in content, titles, meta descriptions, etc.
 * Examples: {store}, {year}, {country}, {total_coupons}
 *
 * ADMIN ACCESS:
 * WordPress Admin → Theme Options → Dynamic Placeholders
 *
 * @package HalaCoupon
 * @subpackage App\Placeholders
 * @version 1.0.0 - Production Ready
 * <AUTHOR> Team
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Main class for handling dynamic placeholders
 */
class HalaCoupon_Dynamic_Placeholders {
    
    /**
     * Available placeholders and their handlers
     */
    private static $placeholders = [];
    
    /**
     * Cache for expensive operations
     */
    private static $cache = [];
    
    /**
     * Initialize the placeholder system
     */
    public static function init() {
        // Register default placeholders
        self::register_default_placeholders();
        
        // Hook into WordPress for SEO-critical content
        add_filter('the_content', [__CLASS__, 'process_content'], 20);
        add_filter('the_title', [__CLASS__, 'process_content'], 20);
        add_filter('widget_text', [__CLASS__, 'process_content'], 20);
        add_filter('wp_nav_menu_items', [__CLASS__, 'process_content'], 20);

        // SEO-critical meta tags and descriptions
        add_filter('wp_title', [__CLASS__, 'process_content'], 20);
        add_filter('document_title_parts', [__CLASS__, 'process_title_parts'], 20);
        add_filter('get_the_excerpt', [__CLASS__, 'process_content'], 20);
        add_filter('bloginfo', [__CLASS__, 'process_content'], 20);
        add_filter('option_blogdescription', [__CLASS__, 'process_content'], 20);

        // Yoast SEO compatibility
        add_filter('wpseo_title', [__CLASS__, 'process_content'], 20);
        add_filter('wpseo_metadesc', [__CLASS__, 'process_content'], 20);
        add_filter('wpseo_opengraph_title', [__CLASS__, 'process_content'], 20);
        add_filter('wpseo_opengraph_desc', [__CLASS__, 'process_content'], 20);

        // RankMath SEO compatibility
        add_filter('rank_math/frontend/title', [__CLASS__, 'process_content'], 20);
        add_filter('rank_math/frontend/description', [__CLASS__, 'process_content'], 20);

        // Theme-specific hooks for coupon/store content
        add_filter('halacoupon_coupon_title', [__CLASS__, 'process_content'], 10);
        add_filter('halacoupon_coupon_description', [__CLASS__, 'process_content'], 10);
        add_filter('halacoupon_store_title', [__CLASS__, 'process_content'], 10);
        add_filter('halacoupon_store_description', [__CLASS__, 'process_content'], 10);

        // Custom hook for manual processing
        add_filter('halacoupon_process_placeholders', [__CLASS__, 'process_content'], 10);

        // Process output buffer for complete page content (last resort for SEO)
        add_action('template_redirect', [__CLASS__, 'start_output_buffer'], 1);
        add_action('wp_footer', [__CLASS__, 'end_output_buffer'], 999);
    }
    
    /**
     * Register a new placeholder
     *
     * @param string $placeholder The placeholder name (without % symbols)
     * @param callable $callback The function to call for replacement
     * @param string $description Optional description for admin
     */
    public static function register_placeholder($placeholder, $callback, $description = '') {
        self::$placeholders[$placeholder] = [
            'callback' => $callback,
            'description' => $description
        ];
    }
    
    /**
     * Register essential placeholders for production
     */
    private static function register_default_placeholders() {

        // Essential Date & Time placeholders
        self::register_placeholder('year', [__CLASS__, 'get_current_year'], 'Current year (e.g., 2024)');
        self::register_placeholder('month', [__CLASS__, 'get_current_month'], 'Current month name');

        // Essential Site Information
        self::register_placeholder('sitetitle', [__CLASS__, 'get_site_title'], 'Site title');
        self::register_placeholder('sitename', [__CLASS__, 'get_site_name'], 'Site name');
        self::register_placeholder('siteurl', [__CLASS__, 'get_site_url'], 'Site URL');
        self::register_placeholder('homeurl', [__CLASS__, 'get_home_url'], 'Home URL');

        // Multisite Support
        self::register_placeholder('network_name', [__CLASS__, 'get_network_name'], 'Network name (multisite)');
        self::register_placeholder('network_url', [__CLASS__, 'get_network_url'], 'Network URL (multisite)');
        self::register_placeholder('blog_id', [__CLASS__, 'get_blog_id'], 'Current blog ID (multisite)');

        // Geographic & Localization
        self::register_placeholder('country', [__CLASS__, 'get_current_country'], 'Current country name');
        self::register_placeholder('currency', [__CLASS__, 'get_current_currency'], 'Current currency');
        self::register_placeholder('currency_symbol', [__CLASS__, 'get_current_currency_symbol'], 'Current currency symbol');

        // Essential Theme Data
        self::register_placeholder('total_coupons', [__CLASS__, 'get_total_coupons'], 'Total number of coupons');
        self::register_placeholder('total_stores', [__CLASS__, 'get_total_stores'], 'Total number of stores');

        // Context-aware placeholders (Essential)
        self::register_placeholder('store', [__CLASS__, 'get_current_store'], 'Current store name (context-aware)');
        self::register_placeholder('store_url', [__CLASS__, 'get_current_store_url'], 'Current store URL (context-aware)');
        self::register_placeholder('category', [__CLASS__, 'get_current_category'], 'Current category name (context-aware)');
        self::register_placeholder('category_url', [__CLASS__, 'get_current_category_url'], 'Current category URL (context-aware)');
        self::register_placeholder('coupon_title', [__CLASS__, 'get_current_coupon_title'], 'Current coupon title (in loop)');
        self::register_placeholder('coupon_code', [__CLASS__, 'get_current_coupon_code'], 'Current coupon code (in loop)');
        self::register_placeholder('coupon_discount', [__CLASS__, 'get_current_coupon_discount'], 'Current coupon discount (in loop)');

        // Allow custom placeholders via filter
        self::$placeholders = apply_filters('halacoupon_register_placeholders', self::$placeholders);
    }
    
    /**
     * Process content and replace placeholders
     *
     * @param string $content The content to process
     * @return string Processed content with placeholders replaced
     */
    public static function process_content($content) {
        if (empty($content) || !is_string($content)) {
            return $content;
        }

        // Find all placeholders in the format {placeholder}
        $pattern = '/\{([a-zA-Z0-9_]+)\}/';

        return preg_replace_callback($pattern, function($matches) {
            $placeholder = $matches[1];

            // Check if placeholder is registered
            if (isset(self::$placeholders[$placeholder])) {
                $callback = self::$placeholders[$placeholder]['callback'];

                // Check cache first
                $cache_key = 'placeholder_' . $placeholder;
                if (isset(self::$cache[$cache_key])) {
                    return self::$cache[$cache_key];
                }

                // Call the callback function
                if (is_callable($callback)) {
                    $result = call_user_func($callback);

                    // Cache the result for this request
                    self::$cache[$cache_key] = $result;

                    return $result;
                }
            }

            // Return original if placeholder not found
            return $matches[0];
        }, $content);
    }
    
    /**
     * Get list of available placeholders
     *
     * @return array List of placeholders with descriptions
     */
    public static function get_available_placeholders() {
        $list = [];
        foreach (self::$placeholders as $placeholder => $data) {
            $list[$placeholder] = [
                'placeholder' => '{' . $placeholder . '}',
                'description' => $data['description']
            ];
        }
        return $list;
    }
    
    // ===========================================
    // PLACEHOLDER CALLBACK FUNCTIONS
    // ===========================================
    
    /**
     * Date & Time Functions
     */
    public static function get_current_year() {
        return date('Y');
    }
    
    public static function get_current_month() {
        return date_i18n('F');
    }
    

    
    /**
     * Site Information Functions
     */
    public static function get_site_title() {
        return get_bloginfo('name');
    }
    
    public static function get_site_name() {
        return get_bloginfo('name');
    }

    public static function get_site_url() {
        return get_site_url();
    }

    public static function get_home_url() {
        return get_home_url();
    }
    
    /**
     * Multisite Functions
     */
    public static function get_network_name() {
        if (is_multisite()) {
            return get_network()->site_name;
        }
        return get_bloginfo('name');
    }
    
    public static function get_network_url() {
        if (is_multisite()) {
            return network_home_url();
        }
        return get_home_url();
    }
    
    public static function get_blog_id() {
        return get_current_blog_id();
    }
    
    /**
     * Geographic & Localization Functions
     */
    public static function get_current_country() {
        // Try to get from various sources
        $country = '';
        
        // Check if GeoIP or similar plugin is available
        if (function_exists('geoip_country_name_by_name')) {
            $ip = self::get_user_ip();
            $country = geoip_country_name_by_name($ip);
        }
        
        // Check WooCommerce if available
        if (empty($country) && class_exists('WooCommerce')) {
            $customer = WC()->customer;
            if ($customer) {
                $country_code = $customer->get_billing_country();
                if ($country_code) {
                    $countries = WC()->countries->get_countries();
                    $country = isset($countries[$country_code]) ? $countries[$country_code] : '';
                }
            }
        }
        
        // Fallback to site locale
        if (empty($country)) {
            $locale = get_locale();
            $country_codes = [
                'en_US' => 'United States',
                'en_GB' => 'United Kingdom',
                'ar' => 'Saudi Arabia',
                'ar_SA' => 'Saudi Arabia',
                'ar_EG' => 'Egypt',
                'ar_AE' => 'United Arab Emirates',
                'fr_FR' => 'France',
                'de_DE' => 'Germany',
                'es_ES' => 'Spain',
                'it_IT' => 'Italy',
                'ja' => 'Japan',
                'zh_CN' => 'China',
                'ru_RU' => 'Russia',
            ];
            
            $country = isset($country_codes[$locale]) ? $country_codes[$locale] : 'Global';
        }
        
        return apply_filters('halacoupon_placeholder_country', $country);
    }
    

    
    public static function get_current_currency() {
        // Check WooCommerce if available
        if (function_exists('get_woocommerce_currency')) {
            return get_woocommerce_currency();
        }
        
        // Fallback based on locale
        $locale = get_locale();
        $locale_parts = explode('_', $locale);
        $country_code = isset($locale_parts[1]) ? $locale_parts[1] : 'US';
        $currencies = [
            'US' => 'USD',
            'GB' => 'GBP',
            'SA' => 'SAR',
            'AE' => 'AED',
            'EG' => 'EGP',
            'FR' => 'EUR',
            'DE' => 'EUR',
            'ES' => 'EUR',
            'IT' => 'EUR',
            'JP' => 'JPY',
            'CN' => 'CNY',
            'RU' => 'RUB',
        ];
        
        return isset($currencies[$country_code]) ? $currencies[$country_code] : 'USD';
    }
    
    public static function get_current_currency_symbol() {
        // Check WooCommerce if available
        if (function_exists('get_woocommerce_currency_symbol')) {
            return get_woocommerce_currency_symbol();
        }
        
        $currency = self::get_current_currency();
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'SAR' => 'ر.س',
            'AED' => 'د.إ',
            'EGP' => 'ج.م',
            'JPY' => '¥',
            'CNY' => '¥',
            'RUB' => '₽',
        ];
        
        return isset($symbols[$currency]) ? $symbols[$currency] : '$';
    }
    

    
    /**
     * Custom Theme Functions
     */
    public static function get_total_coupons() {
        $count = wp_count_posts('coupon');
        return isset($count->publish) ? $count->publish : 0;
    }
    
    public static function get_total_stores() {
        $count = wp_count_terms(['taxonomy' => 'coupon_store', 'hide_empty' => false]);
        return is_wp_error($count) ? 0 : $count;
    }
    


    /**
     * Context-aware placeholder functions
     */

    /**
     * Get current store name based on context
     */
    public static function get_current_store() {
        global $post;

        // Check if we're on a store taxonomy page
        if (is_tax('coupon_store')) {
            $term = get_queried_object();
            return $term ? $term->name : '';
        }

        // Check if we're in a coupon loop/single
        if (is_singular('coupon') || (isset($post) && $post->post_type === 'coupon')) {
            $stores = get_the_terms($post->ID, 'coupon_store');
            if ($stores && !is_wp_error($stores)) {
                return $stores[0]->name;
            }
        }

        // Check global coupon context (for loops)
        if (isset($GLOBALS['halacoupon_current_coupon'])) {
            $coupon_id = $GLOBALS['halacoupon_current_coupon'];
            $stores = get_the_terms($coupon_id, 'coupon_store');
            if ($stores && !is_wp_error($stores)) {
                return $stores[0]->name;
            }
        }

        // Check if we're in a store loop context
        if (isset($GLOBALS['halacoupon_current_store'])) {
            $store = $GLOBALS['halacoupon_current_store'];
            return is_object($store) ? $store->name : $store;
        }

        return '';
    }

    /**
     * Get current store URL based on context
     */
    public static function get_current_store_url() {
        global $post;

        // Check if we're on a store taxonomy page
        if (is_tax('coupon_store')) {
            $term = get_queried_object();
            return $term ? get_term_link($term) : '';
        }

        // Check if we're in a coupon loop/single
        if (is_singular('coupon') || (isset($post) && $post->post_type === 'coupon')) {
            $stores = get_the_terms($post->ID, 'coupon_store');
            if ($stores && !is_wp_error($stores)) {
                return get_term_link($stores[0]);
            }
        }

        // Check global coupon context
        if (isset($GLOBALS['halacoupon_current_coupon'])) {
            $coupon_id = $GLOBALS['halacoupon_current_coupon'];
            $stores = get_the_terms($coupon_id, 'coupon_store');
            if ($stores && !is_wp_error($stores)) {
                return get_term_link($stores[0]);
            }
        }

        // Check if we're in a store loop context
        if (isset($GLOBALS['halacoupon_current_store'])) {
            $store = $GLOBALS['halacoupon_current_store'];
            if (is_object($store)) {
                return get_term_link($store);
            }
        }

        return '';
    }

    /**
     * Get current store description based on context
     */
    public static function get_current_store_description() {
        global $post;

        // Check if we're on a store taxonomy page
        if (is_tax('coupon_store')) {
            $term = get_queried_object();
            return $term ? $term->description : '';
        }

        // Check if we're in a coupon loop/single
        if (is_singular('coupon') || (isset($post) && $post->post_type === 'coupon')) {
            $stores = get_the_terms($post->ID, 'coupon_store');
            if ($stores && !is_wp_error($stores)) {
                return $stores[0]->description;
            }
        }

        // Check global coupon context
        if (isset($GLOBALS['halacoupon_current_coupon'])) {
            $coupon_id = $GLOBALS['halacoupon_current_coupon'];
            $stores = get_the_terms($coupon_id, 'coupon_store');
            if ($stores && !is_wp_error($stores)) {
                return $stores[0]->description;
            }
        }

        // Check if we're in a store loop context
        if (isset($GLOBALS['halacoupon_current_store'])) {
            $store = $GLOBALS['halacoupon_current_store'];
            if (is_object($store)) {
                return $store->description;
            }
        }

        return '';
    }

    /**
     * Get current category name based on context
     */
    public static function get_current_category() {
        global $post;

        // Check if we're on a category taxonomy page
        if (is_tax('coupon_category')) {
            $term = get_queried_object();
            return $term ? $term->name : '';
        }

        // Check if we're in a coupon loop/single
        if (is_singular('coupon') || (isset($post) && $post->post_type === 'coupon')) {
            $categories = get_the_terms($post->ID, 'coupon_category');
            if ($categories && !is_wp_error($categories)) {
                return $categories[0]->name;
            }
        }

        // Check global coupon context
        if (isset($GLOBALS['halacoupon_current_coupon'])) {
            $coupon_id = $GLOBALS['halacoupon_current_coupon'];
            $categories = get_the_terms($coupon_id, 'coupon_category');
            if ($categories && !is_wp_error($categories)) {
                return $categories[0]->name;
            }
        }

        return '';
    }

    /**
     * Get current category URL based on context
     */
    public static function get_current_category_url() {
        global $post;

        // Check if we're on a category taxonomy page
        if (is_tax('coupon_category')) {
            $term = get_queried_object();
            return $term ? get_term_link($term) : '';
        }

        // Check if we're in a coupon loop/single
        if (is_singular('coupon') || (isset($post) && $post->post_type === 'coupon')) {
            $categories = get_the_terms($post->ID, 'coupon_category');
            if ($categories && !is_wp_error($categories)) {
                return get_term_link($categories[0]);
            }
        }

        // Check global coupon context
        if (isset($GLOBALS['halacoupon_current_coupon'])) {
            $coupon_id = $GLOBALS['halacoupon_current_coupon'];
            $categories = get_the_terms($coupon_id, 'coupon_category');
            if ($categories && !is_wp_error($categories)) {
                return get_term_link($categories[0]);
            }
        }

        return '';
    }

    /**
     * Get current coupon title (in loop context)
     */
    public static function get_current_coupon_title() {
        global $post;

        // Check if we're in a coupon context
        if (is_singular('coupon') || (isset($post) && $post->post_type === 'coupon')) {
            return get_the_title($post->ID);
        }

        // Check global coupon context
        if (isset($GLOBALS['halacoupon_current_coupon'])) {
            $coupon_id = $GLOBALS['halacoupon_current_coupon'];
            return get_the_title($coupon_id);
        }

        return '';
    }

    /**
     * Get current coupon code (in loop context)
     */
    public static function get_current_coupon_code() {
        global $post;

        // Check if we're in a coupon context
        if (is_singular('coupon') || (isset($post) && $post->post_type === 'coupon')) {
            return get_post_meta($post->ID, '_coupon_code', true);
        }

        // Check global coupon context
        if (isset($GLOBALS['halacoupon_current_coupon'])) {
            $coupon_id = $GLOBALS['halacoupon_current_coupon'];
            return get_post_meta($coupon_id, '_coupon_code', true);
        }

        return '';
    }

    /**
     * Get current coupon discount (in loop context)
     */
    public static function get_current_coupon_discount() {
        global $post;

        // Check if we're in a coupon context
        if (is_singular('coupon') || (isset($post) && $post->post_type === 'coupon')) {
            return get_post_meta($post->ID, '_coupon_discount_text', true);
        }

        // Check global coupon context
        if (isset($GLOBALS['halacoupon_current_coupon'])) {
            $coupon_id = $GLOBALS['halacoupon_current_coupon'];
            return get_post_meta($coupon_id, '_coupon_discount_text', true);
        }

        return '';
    }

    /**
     * Process document title parts for SEO
     */
    public static function process_title_parts($title_parts) {
        if (is_array($title_parts)) {
            foreach ($title_parts as $key => $part) {
                $title_parts[$key] = self::process_content($part);
            }
        }
        return $title_parts;
    }

    /**
     * Start output buffering for complete page processing
     */
    public static function start_output_buffer() {
        // Only process on frontend, not admin
        if (!is_admin() && !wp_doing_ajax()) {
            ob_start([__CLASS__, 'process_final_output']);
        }
    }

    /**
     * End output buffering
     */
    public static function end_output_buffer() {
        // This will trigger the callback automatically
    }

    /**
     * Process final output for any remaining placeholders (SEO-critical)
     */
    public static function process_final_output($content) {
        // Only process if content contains placeholders
        if (strpos($content, '{') !== false && strpos($content, '}') !== false) {
            // Process the entire page content
            $content = self::process_content($content);
        }
        return $content;
    }

    /**
     * Helper function to get user IP
     */
    private static function get_user_ip() {
        $ip_keys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '127.0.0.1';
    }
}

// Initialize the system
HalaCoupon_Dynamic_Placeholders::init();

/**
 * Admin functionality for placeholders
 */
class HalaCoupon_Placeholders_Admin {

    public static function init() {
        add_action('wp_ajax_test_placeholder', [__CLASS__, 'ajax_test_placeholder']);
    }



    public static function ajax_test_placeholder() {
        // Security check
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        if (isset($_POST['content'])) {
            $content = sanitize_textarea_field($_POST['content']);

            if (!empty($content)) {
                try {
                    $processed = HalaCoupon_Dynamic_Placeholders::process_content($content);
                    wp_send_json_success(nl2br(esc_html($processed)));
                } catch (Exception $e) {
                    wp_send_json_error('Error processing placeholders');
                }
            } else {
                wp_send_json_error('No content provided');
            }
        } else {
            wp_send_json_error('Invalid request');
        }
    }
}

// Initialize admin if in admin area
if (is_admin()) {
    HalaCoupon_Placeholders_Admin::init();
}

/**
 * Add placeholders section to Redux theme options
 */
function halacoupon_add_placeholders_to_redux($sections) {
    // Only add if Redux is available
    if (!class_exists('ReduxFramework')) {
        return $sections;
    }

    $placeholders = HalaCoupon_Dynamic_Placeholders::get_available_placeholders();

    // Create description with all available placeholders
    $placeholder_list = '<div style="background: #f9f9f9; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    $placeholder_list .= '<h4 style="margin-top: 0;">' . __('Available Placeholders:', 'halacoupon') . '</h4>';
    $placeholder_list .= '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 10px;">';

    foreach ($placeholders as $data) {
        $placeholder_list .= '<div style="background: white; padding: 10px; border-radius: 3px; border-left: 3px solid #0073aa;">';
        $placeholder_list .= '<code style="background: #e1f5fe; padding: 2px 6px; border-radius: 3px; font-weight: bold;">' . esc_html($data['placeholder']) . '</code>';
        $placeholder_list .= '<br><small style="color: #666; margin-top: 5px; display: block;">' . esc_html($data['description']) . '</small>';
        $placeholder_list .= '</div>';
    }

    $placeholder_list .= '</div></div>';

    // Add placeholders section to Redux
    $sections[] = array(
        'title'  => __('Dynamic Placeholders', 'halacoupon'),
        'desc'   => __('Use these placeholders in your content, titles, or anywhere on your website. They will be automatically replaced with dynamic data for better SEO.', 'halacoupon'),
        'icon'   => 'el-icon-magic',
        'submenu' => true,
        'fields' => array(
            array(
                'id'   => 'placeholders_info',
                'type' => 'info',
                'title' => __('How to Use Placeholders', 'halacoupon'),
                'desc' => __('Simply use the format {placeholder} in your content. All processing is done server-side for optimal SEO performance.', 'halacoupon'),
            ),
            array(
                'id'   => 'placeholders_list',
                'type' => 'raw',
                'title' => __('Essential Placeholders for Production', 'halacoupon'),
                'content' => $placeholder_list,
            ),
            array(
                'id'   => 'placeholders_examples',
                'type' => 'info',
                'title' => __('Usage Examples', 'halacoupon'),
                'desc' => '<strong>' . __('SEO Title:', 'halacoupon') . '</strong> {store} Coupons {year} - Save Money in {country}<br>' .
                         '<strong>' . __('Page Header:', 'halacoupon') . '</strong> Welcome to {sitetitle} - {total_coupons} deals available<br>' .
                         '<strong>' . __('Store Page:', 'halacoupon') . '</strong> Get exclusive {store} deals in {year}<br>' .
                         '<strong>' . __('Multisite:', 'halacoupon') . '</strong> Visit our network: {network_name}',
            ),
            array(
                'id'   => 'placeholders_test_area',
                'type' => 'raw',
                'title' => __('Test Placeholders', 'halacoupon'),
                'content' => '
                <div id="halacoupon-placeholder-tester" style="background: #f9f9f9; padding: 20px; border-radius: 5px; margin: 10px 0;">
                    <h4>' . __('Live Placeholder Tester', 'halacoupon') . '</h4>
                    <p>' . __('Enter content with placeholders below to see the live results:', 'halacoupon') . '</p>

                    <div style="margin: 15px 0;">
                        <label for="test-content-redux" style="display: block; font-weight: bold; margin-bottom: 5px;">' . __('Test Content:', 'halacoupon') . '</label>
                        <textarea id="test-content-redux" rows="6" style="width: 100%; font-family: monospace;" placeholder="' . __('Enter content with placeholders...', 'halacoupon') . '">Welcome to {sitetitle} in {year}!
We are located in {country} and use {currency} currency.
Total coupons: {total_coupons}
Current store: {store}</textarea>
                    </div>

                    <div style="margin: 15px 0;">
                        <button type="button" id="process-test-redux" class="button button-primary" style="margin-right: 10px;">' . __('Process Placeholders', 'halacoupon') . '</button>
                        <button type="button" id="clear-test-redux" class="button">' . __('Clear', 'halacoupon') . '</button>
                    </div>

                    <div style="margin: 15px 0;">
                        <label style="display: block; font-weight: bold; margin-bottom: 5px;">' . __('Processed Result:', 'halacoupon') . '</label>
                        <div id="test-result-redux" style="background: white; padding: 15px; border: 1px solid #ddd; border-radius: 3px; min-height: 80px; font-family: inherit;">
                            <em style="color: #666;">' . __('Enter content above and click "Process Placeholders" to see the result...', 'halacoupon') . '</em>
                        </div>
                    </div>
                </div>

                <script>
                jQuery(document).ready(function($) {
                    // Process test content
                    $("#process-test-redux").on("click", function() {
                        var content = $("#test-content-redux").val();

                        if (!content.trim()) {
                            alert("' . __('Please enter some content to test.', 'halacoupon') . '");
                            return;
                        }

                        $("#test-result-redux").html("<em>' . __('Processing...', 'halacoupon') . '</em>");

                        $.post(ajaxurl, {
                            action: "test_placeholder",
                            content: content
                        }, function(response) {
                            if (response.success) {
                                $("#test-result-redux").html(response.data);
                            } else {
                                $("#test-result-redux").html("<em style=\"color: #d63638;\">' . __('Error processing content.', 'halacoupon') . '</em>");
                            }
                        }).fail(function() {
                            $("#test-result-redux").html("<em style=\"color: #d63638;\">' . __('Connection error. Please try again.', 'halacoupon') . '</em>");
                        });
                    });

                    // Clear test
                    $("#clear-test-redux").on("click", function() {
                        $("#test-content-redux").val("");
                        $("#test-result-redux").html("<em style=\"color: #666;\">' . __('Enter content above and click \"Process Placeholders\" to see the result...', 'halacoupon') . '</em>");
                    });
                });
                </script>',
            ),
        )
    );

    return $sections;
}
add_filter('halacoupon_more_options_settings', 'halacoupon_add_placeholders_to_redux');

/**
 * Helper function to process placeholders manually
 *
 * @param string $content Content to process
 * @return string Processed content
 */
function halacoupon_process_placeholders($content) {
    return apply_filters('halacoupon_process_placeholders', $content);
}

/**
 * Helper function to get available placeholders
 *
 * @return array Available placeholders
 */
function halacoupon_get_available_placeholders() {
    return HalaCoupon_Dynamic_Placeholders::get_available_placeholders();
}

/**
 * Helper function to register custom placeholder
 *
 * @param string $placeholder Placeholder name
 * @param callable $callback Callback function
 * @param string $description Description
 */
function halacoupon_register_placeholder($placeholder, $callback, $description = '') {
    HalaCoupon_Dynamic_Placeholders::register_placeholder($placeholder, $callback, $description);
}

/**
 * Helper function to set current store context for loops
 *
 * @param object|string $store Store object or name
 */
function halacoupon_set_store_context($store) {
    $GLOBALS['halacoupon_current_store'] = $store;
}

/**
 * Helper function to set current coupon context for loops
 *
 * @param int $coupon_id Coupon ID
 */
function halacoupon_set_coupon_context($coupon_id) {
    $GLOBALS['halacoupon_current_coupon'] = $coupon_id;
}

/**
 * Helper function to clear context
 */
function halacoupon_clear_context() {
    unset($GLOBALS['halacoupon_current_store']);
    unset($GLOBALS['halacoupon_current_coupon']);
}

/**
 * Enhanced helper for processing content with context
 *
 * @param string $content Content to process
 * @param array $context Optional context data
 * @return string Processed content
 */
function halacoupon_process_placeholders_with_context($content, $context = []) {
    // Set temporary context if provided
    $old_store = isset($GLOBALS['halacoupon_current_store']) ? $GLOBALS['halacoupon_current_store'] : null;
    $old_coupon = isset($GLOBALS['halacoupon_current_coupon']) ? $GLOBALS['halacoupon_current_coupon'] : null;

    if (isset($context['store'])) {
        $GLOBALS['halacoupon_current_store'] = $context['store'];
    }
    if (isset($context['coupon_id'])) {
        $GLOBALS['halacoupon_current_coupon'] = $context['coupon_id'];
    }

    // Process content
    $processed = halacoupon_process_placeholders($content);

    // Restore old context
    if ($old_store !== null) {
        $GLOBALS['halacoupon_current_store'] = $old_store;
    } else {
        unset($GLOBALS['halacoupon_current_store']);
    }

    if ($old_coupon !== null) {
        $GLOBALS['halacoupon_current_coupon'] = $old_coupon;
    } else {
        unset($GLOBALS['halacoupon_current_coupon']);
    }

    return $processed;
}

/**
 * Process and echo content with placeholders (for templates)
 *
 * @param string $content Content to process and echo
 * @param array $context Optional context data
 */
function halacoupon_echo_placeholders($content, $context = []) {
    echo halacoupon_process_placeholders_with_context($content, $context);
}

/**
 * Get processed placeholder value directly
 *
 * @param string $placeholder Placeholder name (without braces)
 * @param mixed $fallback Fallback value if placeholder not found
 * @return string Processed value
 */
function halacoupon_get_placeholder($placeholder, $fallback = '') {
    $content = '{' . $placeholder . '}';
    $processed = halacoupon_process_placeholders($content);

    // If not processed (still has braces), return fallback
    if ($processed === $content) {
        return $fallback;
    }

    return $processed;
}
