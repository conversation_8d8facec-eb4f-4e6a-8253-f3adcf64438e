<?php
/**
 * SEO-Optimized Placeholder Usage Examples
 *
 * This file demonstrates how to properly use placeholders for SEO-critical content
 * All examples here are processed server-side for optimal SEO performance
 *
 * @package HalaCoupon
 * @subpackage App\Placeholders
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Example: SEO-optimized store page title
 */
function halacoupon_seo_store_title($title) {
    if (is_tax('coupon_store')) {
        // This will be processed server-side for SEO
        $seo_title = '{store} Coupons & Deals {year} - Save Money in {country}';
        return halacoupon_process_placeholders($seo_title);
    }
    return $title;
}
add_filter('wp_title', 'halacoupon_seo_store_title', 25);
add_filter('wpseo_title', 'halacoupon_seo_store_title', 25);

/**
 * Example: SEO-optimized meta descriptions
 */
function halacoupon_seo_meta_description($description) {
    if (is_tax('coupon_store')) {
        $seo_description = 'Get exclusive {store} coupons and deals in {year}. Save money with verified discount codes in {country}. {total_coupons} active offers available.';
        return halacoupon_process_placeholders($seo_description);
    }
    
    if (is_tax('coupon_category')) {
        $seo_description = 'Best {category} deals and coupons in {year}. Save money in {country} with {currency} currency. {total_coupons} verified offers.';
        return halacoupon_process_placeholders($seo_description);
    }
    
    return $description;
}
add_filter('wpseo_metadesc', 'halacoupon_seo_meta_description', 25);
add_filter('rank_math/frontend/description', 'halacoupon_seo_meta_description', 25);

/**
 * Example: Process coupon titles in loops (SEO-critical)
 */
function halacoupon_process_coupon_title($title, $coupon_id) {
    // Set context for the coupon
    halacoupon_set_coupon_context($coupon_id);
    
    // Process any placeholders in the title
    $processed_title = halacoupon_process_placeholders($title);
    
    // Clear context
    halacoupon_clear_context();
    
    return $processed_title;
}

/**
 * Example: Process store descriptions (SEO-critical)
 */
function halacoupon_process_store_description($description, $store) {
    // Set context for the store
    halacoupon_set_store_context($store);
    
    // Process any placeholders in the description
    $processed_description = halacoupon_process_placeholders($description);
    
    // Clear context
    halacoupon_clear_context();
    
    return $processed_description;
}

/**
 * Example: SEO-optimized breadcrumbs
 */
function halacoupon_seo_breadcrumbs() {
    $breadcrumbs = [];
    
    // Home
    $breadcrumbs[] = '<a href="{homeurl}">{sitetitle}</a>';
    
    if (is_tax('coupon_store')) {
        $breadcrumbs[] = '<a href="{homeurl}/stores/">Stores</a>';
        $breadcrumbs[] = '<span>{store}</span>';
    } elseif (is_tax('coupon_category')) {
        $breadcrumbs[] = '<a href="{homeurl}/categories/">Categories</a>';
        $breadcrumbs[] = '<span>{category}</span>';
    } elseif (is_singular('coupon')) {
        $breadcrumbs[] = '<a href="{homeurl}/coupons/">Coupons</a>';
        $breadcrumbs[] = '<a href="{store_url}">{store}</a>';
        $breadcrumbs[] = '<span>{coupon_title}</span>';
    }
    
    // Process all breadcrumbs server-side for SEO
    $processed_breadcrumbs = array_map('halacoupon_process_placeholders', $breadcrumbs);
    
    return '<nav class="breadcrumbs">' . implode(' > ', $processed_breadcrumbs) . '</nav>';
}

/**
 * Example: SEO-optimized page headers
 */
function halacoupon_seo_page_header() {
    $header_content = '';
    
    if (is_tax('coupon_store')) {
        $header_content = '
        <h1>{store} Coupons & Deals</h1>
        <p>Exclusive {store} discount codes and offers in {year}. Save money in {country} with verified coupons.</p>
        <div class="stats">
            <span>Total Offers: {total_coupons}</span>
            <span>Currency: {currency_symbol}</span>
            <span>Updated: {date}</span>
        </div>';
    } elseif (is_tax('coupon_category')) {
        $header_content = '
        <h1>{category} Deals & Coupons</h1>
        <p>Best {category} offers and discount codes in {year}. Save money in {country}.</p>';
    } elseif (is_singular('coupon')) {
        $header_content = '
        <h1>{coupon_title}</h1>
        <p>Exclusive deal from {store} - {coupon_discount}</p>
        <div class="coupon-meta">
            <span>Store: <a href="{store_url}">{store}</a></span>
            <span>Category: <a href="{category_url}">{category}</a></span>
        </div>';
    }
    
    // Process server-side for SEO
    return halacoupon_process_placeholders($header_content);
}

/**
 * Example: Template function for coupon loops
 */
function halacoupon_render_coupon_with_placeholders($coupon_id, $template) {
    // Set coupon context
    halacoupon_set_coupon_context($coupon_id);
    
    // Example template with placeholders
    $default_template = '
    <div class="coupon-card">
        <h3>{coupon_title}</h3>
        <p class="store">From <a href="{store_url}">{store}</a></p>
        <p class="discount">{coupon_discount}</p>
        <div class="code">{coupon_code}</div>
        <p class="category">Category: <a href="{category_url}">{category}</a></p>
    </div>';
    
    $template = $template ?: $default_template;
    
    // Process server-side for SEO
    $processed = halacoupon_process_placeholders($template);
    
    // Clear context
    halacoupon_clear_context();
    
    return $processed;
}

/**
 * Example: Template function for store loops
 */
function halacoupon_render_store_with_placeholders($store, $template) {
    // Set store context
    halacoupon_set_store_context($store);
    
    // Example template with placeholders
    $default_template = '
    <div class="store-card">
        <h3><a href="{store_url}">{store}</a></h3>
        <p>{store_description}</p>
        <div class="stats">
            <span>Total Coupons: {total_coupons}</span>
            <span>Country: {country}</span>
        </div>
    </div>';
    
    $template = $template ?: $default_template;
    
    // Process server-side for SEO
    $processed = halacoupon_process_placeholders($template);
    
    // Clear context
    halacoupon_clear_context();
    
    return $processed;
}

/**
 * Example: SEO-optimized schema markup
 */
function halacoupon_seo_schema_markup() {
    if (is_singular('coupon')) {
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'Offer',
            'name' => '{coupon_title}',
            'description' => '{coupon_title} from {store}',
            'seller' => [
                '@type' => 'Organization',
                'name' => '{store}'
            ],
            'validFrom' => '{date}',
            'priceCurrency' => '{currency}',
            'availability' => 'https://schema.org/InStock'
        ];
        
        // Process schema data server-side
        $processed_schema = json_encode($schema);
        $processed_schema = halacoupon_process_placeholders($processed_schema);
        
        echo '<script type="application/ld+json">' . $processed_schema . '</script>';
    }
}
add_action('wp_head', 'halacoupon_seo_schema_markup');

/**
 * Example: How to use in template files
 */
function halacoupon_template_examples() {
    // Example 1: Direct processing in templates
    echo halacoupon_process_placeholders('<h1>Welcome to {sitetitle} - {year}</h1>');
    
    // Example 2: With context
    halacoupon_echo_placeholders('<h2>{store} Deals</h2>', ['store' => $current_store]);
    
    // Example 3: Get single placeholder value
    $site_title = halacoupon_get_placeholder('sitetitle', 'Default Site');
    
    // Example 4: In loops
    foreach ($coupons as $coupon) {
        echo halacoupon_render_coupon_with_placeholders($coupon->ID, '
            <div class="coupon">
                <h3>{coupon_title}</h3>
                <p>From {store} - {coupon_discount}</p>
            </div>
        ');
    }
}

/**
 * Example: WordPress hooks for automatic processing
 */
function halacoupon_auto_process_content($content) {
    // This automatically processes any content with placeholders
    return halacoupon_process_placeholders($content);
}

// Hook into various WordPress filters for automatic processing
add_filter('the_content', 'halacoupon_auto_process_content', 20);
add_filter('the_title', 'halacoupon_auto_process_content', 20);
add_filter('the_excerpt', 'halacoupon_auto_process_content', 20);

/**
 * Example: Custom post meta processing
 */
function halacoupon_process_custom_fields($value, $post_id, $meta_key) {
    // Process specific meta fields that might contain placeholders
    $placeholder_fields = [
        '_coupon_seo_title',
        '_coupon_seo_description',
        '_store_seo_title',
        '_store_seo_description'
    ];
    
    if (in_array($meta_key, $placeholder_fields)) {
        return halacoupon_process_placeholders($value);
    }
    
    return $value;
}
add_filter('get_post_metadata', 'halacoupon_process_custom_fields', 20, 3);

/**
 * Example: Widget content processing
 */
function halacoupon_process_widget_content($content) {
    return halacoupon_process_placeholders($content);
}
add_filter('widget_text', 'halacoupon_process_widget_content', 20);
add_filter('widget_title', 'halacoupon_process_widget_content', 20);

/**
 * Example: Menu item processing
 */
function halacoupon_process_menu_items($items, $args) {
    foreach ($items as $item) {
        $item->title = halacoupon_process_placeholders($item->title);
        $item->description = halacoupon_process_placeholders($item->description);
    }
    return $items;
}
add_filter('wp_nav_menu_objects', 'halacoupon_process_menu_items', 20, 2);
