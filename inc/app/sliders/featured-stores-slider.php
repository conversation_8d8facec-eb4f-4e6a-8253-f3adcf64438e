<?php
/**
 * Featured Stores Slider - Modernized with Splide.js
 *
 * Modern slider for displaying featured stores with creative design
 * Features: Glass morphism, brand colors, hover effects, responsive design
 *
 * @package HalaCoupon
 * @subpackage App\Sliders
 * @version 2.0.0 - Modernized
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Display modern featured stores slider with Splide.js
 */
if (!function_exists('halacoupon_get_featured_stores_slider')) {
    function halacoupon_get_featured_stores_slider() {
        // Get featured stores
        $featured_stores = halacoupon_get_featured_stores(12);

        if (!empty($featured_stores) && !is_wp_error($featured_stores)) {

            // Enqueue Splide assets conditionally
            halacoupon_enqueue_splide_assets();

			$direction = is_rtl() ? 'rtl' : 'ltr';

            echo '<div class="featured-stores-slider-container relative mx-auto mb-12">';

            echo '<div class="splide store-slider" id="featured-stores-slider" dir="' . esc_attr($direction) . '">';
            echo '<div class="splide__track">';
            echo '<ul class="splide__list">';

            foreach ($featured_stores as $store) {
                $store_image = get_term_meta($store->term_id, '_ags_store_image', true);
                $store_link = get_term_link($store, 'coupon_store');
                $store_name = $store->name;
                $coupon_counts = halacoupon_get_coupon_counts_by_type($store->term_id, 'coupon_store');
                $total_coupons = ($coupon_counts['code'] ?? 0) + ($coupon_counts['sale'] ?? 0);

                echo '<li class="splide__slide">';
                echo '<div class="featured-store-item group px-1">';
                echo '<div class="relative bg-white rounded-xl shadow-soft hover:shadow-medium border border-gray-100 overflow-hidden transition-all duration-300 hover:-translate-y-2 hover:border-secondary-200">';

                // Background gradient overlay
                echo '<div class="absolute inset-0 bg-gradient-to-br from-secondary-50 to-accent-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>';

                // Content container
                echo '<div class="relative p-6 text-center">';

                // Store logo with enhanced design
                echo '<div class="relative w-20 h-20 mx-auto mb-4">';
                echo '<div class="absolute inset-0 bg-gradient-secondary rounded-2xl opacity-20 group-hover:opacity-30 transition-opacity duration-300"></div>';
                echo '<a href="' . esc_url($store_link) . '" class="block relative">';
                if ($store_image) {
                    echo '<img src="' . esc_url($store_image) . '" alt="' . esc_attr($store_name) . '" class="relative w-full h-full object-cover rounded-2xl group-hover:scale-110 transition-transform duration-300 shadow-soft" loading="lazy">';
                } else {
                    echo '<div class="relative w-full h-full bg-gradient-to-br from-secondary-100 to-secondary-200 rounded-2xl flex items-center justify-center text-2xl font-bold text-secondary-700 group-hover:scale-110 transition-transform duration-300 shadow-soft">' . esc_html(substr($store_name, 0, 1)) . '</div>';
                }
                echo '</a>';
                echo '</div>';

                // Store info
                echo '<div class="space-y-3">';

                // Store name
                echo '<h3 class="text-lg font-bold text-text group-hover:text-secondary transition-colors duration-300">';
                echo '<a href="' . esc_url($store_link) . '" class="hover:text-secondary transition-colors duration-300">' . esc_html($store_name) . '</a>';
                echo '</h3>';

                // Store stats with modern design
                echo '<div class="flex items-center justify-center gap-4 text-sm">';
                echo '<div class="flex items-center gap-1 text-gray-600 group-hover:text-secondary transition-colors duration-300">';
                echo '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>';
                echo '</svg>';
                echo '<span class="font-medium">' . esc_html($coupon_counts['code']) . '</span>';
                echo '</div>';
                echo '<div class="w-1 h-1 bg-gray-300 rounded-full"></div>';
                echo '<div class="flex items-center gap-1 text-gray-600 group-hover:text-secondary transition-colors duration-300">';
                echo '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>';
                echo '</svg>';
                echo '<span class="font-medium">' . esc_html($coupon_counts['sale']) . '</span>';
                echo '</div>';
                echo '</div>';

                // Total offers badge
                echo '<div class="inline-flex items-center gap-2 bg-gradient-secondary text-white px-4 py-2 rounded-full text-sm font-semibold shadow-soft">';
                echo '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>';
                echo '</svg>';
                echo '<span>' . esc_html($total_coupons) . ' ' . esc_html__('Deals', 'halacoupon') . '</span>';
                echo '</div>';

                echo '</div>'; // End store info
                echo '</div>'; // End content container

                // Hover arrow
                echo '<div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-2 group-hover:translate-x-0">';
                echo '<div class="w-8 h-8 bg-gradient-secondary rounded-full flex items-center justify-center shadow-lg">';
                echo '<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>';
                echo '</svg>';
                echo '</div>';
                echo '</div>';

                echo '</div>'; // End store card
                echo '</div>'; // End featured-store-item
                echo '</li>';
            }

            echo '</ul>'; // Close splide__list
            echo '</div>'; // Close splide__track
            echo '</div>'; // Close splide
            echo '</div>'; // Close featured-stores-slider-container

            // Initialize Splide with store-specific configuration
            $config = halacoupon_get_splide_config('store-slider');
            echo halacoupon_generate_splide_script('#featured-stores-slider', $config);

        } else {
            echo '<div class="no-featured-stores">';
            echo '<p>' . esc_html__('No featured stores available.', 'halacoupon') . '</p>';
            echo '</div>';
        }
    }
}

// Mark this module as loaded
add_filter('halacoupon_module_featured_stores_slider_loaded', '__return_true');
add_filter('halacoupon_loaded_modules', function($modules) {
    $modules[] = 'sliders/featured-stores-slider';
    return $modules;
});
