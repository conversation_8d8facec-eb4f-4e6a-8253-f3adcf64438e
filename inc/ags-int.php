<?php
// /**
// * *********************************************
// * *                                           *
// * *                                           *
// * *     ▄████████    ▄██████▄     ▄████████   *
// * *    ███    ███   ███    ███   ███    ███   *
// * *    ███    ███   ███    █▀    ███    █▀    *
// * *    ███    ███  ▄███          ███          *
// * *  ▀███████████ ▀▀███ ████▄  ▀███████████   *
// * *    ███    ███   ███    ███          ███   *
// * *    ███    ███   ███    ███    ▄█    ███   *
// * *    ███    █▀    ████████▀   ▄████████▀    *
// * *                                           *
// * *    Abdullah G ********** +201001248698    *
// * *                                           *
// * *********************************************
// */



if (!defined('halacoupon_ASSETS_PATH')) {
    define('halacoupon_ASSETS_PATH', get_template_directory_uri() . '/assets/');
}



function halacoupon_compile_and_enqueue_assets() {
    $theme_version = wp_get_theme()->get('Version');

    // =============================================================================
    // COMPILED CSS BUNDLES (Laravel Mix) - NOW WORKING!
    // =============================================================================

    // Enqueue Rubik fonts first (highest priority)
    wp_enqueue_style('halacoupon-fonts', get_template_directory_uri() . '/assets/css/fonts/fonts.css', [], $theme_version);

    // Enqueue main compiled CSS bundle (includes Tailwind + all theme CSS files)
    wp_enqueue_style('halacoupon-main', get_template_directory_uri() . '/assets/dist/css/main.css', ['halacoupon-fonts'], $theme_version);

    // Enqueue RTL styles if needed
    if (is_rtl()) {
        wp_enqueue_style('halacoupon-rtl', get_template_directory_uri() . '/assets/dist/css/rtl.css', ['halacoupon-main'], $theme_version);
    }

    // Enqueue theme's main style.css (for WordPress theme requirements)
	wp_enqueue_style( 'halacoupon_style', halacoupon_THEME_URI . 'style.css', ['halacoupon-main'], $theme_version );



	if(is_tax('coupon_store') || is_tax('coupon_category')) {
		wp_enqueue_style( 'halacoupon_store_styles', halacoupon_CSS_PATH . 'store.css', false, $theme_version );
		wp_enqueue_style( 'halacoupon_store_social_links', halacoupon_CSS_PATH . 'components/store-social-links.css', array('halacoupon_store_styles'), $theme_version );
		wp_enqueue_script('halacoupon_rating', halacoupon_JS_PATH . 'jq-rating.js', array('jquery'), null, true);
		wp_enqueue_script('halacoupon_stores', halacoupon_JS_PATH . 'stores.js', array('jquery'), $theme_version, true);

		// Enqueue store content blocks JavaScript for enhanced navigation
		wp_enqueue_script('halacoupon_store_content_blocks', halacoupon_JS_PATH . 'store-content-blocks.js', array('jquery', 'halacoupon_stores'), $theme_version, true);

        wp_localize_script('halacoupon_rating', 'halacouponRating', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce'    => wp_create_nonce('halacoupon_rating_nonce')
        ));

	}

	// Enqueue rating scripts for review pages
	if (is_singular('review') || is_post_type_archive('review')) {
		wp_enqueue_script('halacoupon_rating', halacoupon_JS_PATH . 'jq-rating.js', array('jquery'), null, true);

        wp_localize_script('halacoupon_rating', 'halacouponRating', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce'    => wp_create_nonce('halacoupon_rating_nonce')
        ));
        
	}

	if ( is_singular() && comments_open() && get_option( 'thread_comments' ) ) {
		wp_enqueue_script( 'comment-reply' );
	}



    // =============================================================================
    // TEMPORARY: REVERT TO ORIGINAL JS ENQUEUEING WHILE FIXING COMPILATION
    // =============================================================================

	// Enqueue global script first to provide ST object
	wp_enqueue_script( 'halacoupon_global', halacoupon_JS_PATH . 'global.js', array( 'jquery' ),  $theme_version, true );

	// Enqueue other scripts with dependency on global script
	wp_enqueue_script( 'halacoupon_header', halacoupon_JS_PATH . 'header.js', array( 'jquery', 'halacoupon_global' ),  $theme_version, true );
	wp_enqueue_script( 'halacoupon_coupons', halacoupon_JS_PATH . 'coupons.js', array( 'jquery', 'halacoupon_global' ),  $theme_version, true );
	wp_enqueue_script( 'halacoupon_interactive', halacoupon_JS_PATH . 'interactive.js', array( 'jquery', 'halacoupon_global' ),  $theme_version, true );
	wp_enqueue_script( 'halacoupon_modals', halacoupon_JS_PATH . 'modals.js', array( 'jquery', 'halacoupon_global' ),  $theme_version, true );
	wp_enqueue_script( 'halacoupon_css_filters', halacoupon_JS_PATH . 'css-filters.js', array( 'jquery', 'halacoupon_global' ),  $theme_version, true );
	wp_enqueue_script( 'halacoupon_main', halacoupon_JS_PATH . 'main.js', array( 'jquery', 'halacoupon_global' ),  $theme_version, true );

	// Get store slug from configuration
	$store_slug = trim( halacoupon_get_option( 'rewrite_store_slug', '' ) );
	if ( ! $store_slug ) {
		$store_slug = 'discount-codes';
	}

	$localize = array(
		'ajax_url'        => admin_url( 'admin-ajax.php' ),
		'loading_message' => __( 'Loading...', 'halacoupon' ),
		'readLess'        => __( 'Read Less', 'halacoupon' ),
        'error_message'   => __( 'Error loading stores. Please try again.', 'halacoupon' ),
		'home_url'        => home_url( '/' ),
		'store_slug'      => $store_slug,
		'store_url_path'  => '/' . $store_slug . '/',
		'enable_single'   => halacoupon_is_single_enable(),
		'auto_open_coupon_modal'   => halacoupon_get_option( 'auto_open_coupon_modal' ) ? 1 : '',
		'vote_expires'    => apply_filters( 'st_coupon_vote_expires', 7 ), // 7 days
		'_wpnonce'        => wp_create_nonce(),
		'_coupon_nonce'   => wp_create_nonce( '_coupon_nonce' ),
		'user_logedin'    => is_user_logged_in(),
		'added_favorite'  => esc_html__( 'Favorited', 'halacoupon' ),
		'add_favorite'    => esc_html__( 'Favorite This Store', 'halacoupon' ),
		'save_coupon'     => esc_html__( 'Save this coupon', 'halacoupon' ),
		'saved_coupon'    => esc_html__( 'Coupon Saved', 'halacoupon' ),
		'no_results'      => esc_html__( 'No results found', 'halacoupon' ),

		'copied'          => esc_html__( 'Copied', 'halacoupon' ),
		'copy'            => esc_html__( 'Copy', 'halacoupon' ),
		'sale_prev_tab'   => halacoupon_get_option( 'sale_prev_tab', true ) ? 1 : 0,
		'code_prev_tab'   => halacoupon_get_option( 'code_prev_tab', true ) ? 1 : 0,
		'coupon_click_action'   => halacoupon_get_option( 'coupon_click_action', 'prev' ),
		'share_id'        => 0,
		'header_sticky'   => halacoupon_get_option( 'header_sticky', false ),
		// Enhanced search modal translations
		'try_different'   => esc_html__( 'Try a different search term', 'halacoupon' ),
		'store'           => esc_html__( 'Store', 'halacoupon' ),
		'coupon'          => esc_html__( 'Coupon', 'halacoupon' ),
		'click_to_view'   => esc_html__( 'Click to view', 'halacoupon' ),
		'search_error'    => esc_html__( 'Search error occurred', 'halacoupon' ),
		'try_again'       => esc_html__( 'Please try again', 'halacoupon' ),
		'try_categories'  => esc_html__( 'Try browsing our quick categories below!', 'halacoupon' ),
		'retry_search'    => esc_html__( 'Try Again', 'halacoupon' ),
		'is_rtl'          => is_rtl(),
	);
	$list = '';
	if ( is_user_logged_in() ) {
		$user = wp_get_current_user();
		$list  = get_user_meta( $user->ID, '_ags_saved_coupons', true );
		$stores = get_user_meta( $user->ID, '_ags_favorite_stores', true );
		$localize['my_saved_coupons'] = explode( ',', $list );
		$localize['my_favorite_stores'] = explode( ',', $stores );
	} else {
		$localize['my_saved_coupons'] = array();
		$localize['my_favorite_stores'] = array();
	}

	if ( is_tax( 'coupon_store' ) ) {
		global $wp_rewrite;
		$coupon_id = '';

		if ( $wp_rewrite->using_permalinks() ) {
			$share_id = get_query_var('share_id');
			$coupon_id = get_query_var('coupon_id');

			// Remove any # from coupon_id
			$coupon_id = ltrim($coupon_id, '#');
	
		} else {
			 
			if ( isset( $_GET['share_id'] ) && isset( $_GET['coupon_id'] ) ) {
				 
				$share_id = absint( $_GET['share_id'] );
				$coupon_id = absint( $_GET['coupon_id'] );
			} else {
				 
				$share_id = 0;
				$coupon_id = 0;
			}
		}

		$localize['share_id'] = $share_id;
		$localize['coupon_id'] = $coupon_id;
	}
	

	if ( $localize['enable_single'] ) {
		if ( is_singular( 'coupon' ) ) {
			global $post;
			$localize['current_coupon_id'] = $post->ID;
		}
	}

	$localize['my_saved_coupons'] = explode( ',', $list );
	wp_localize_script( 'halacoupon_global', 'ST', apply_filters( 'halacoupon_localize_script', $localize ) );


}
add_action('wp_enqueue_scripts', 'halacoupon_compile_and_enqueue_assets');

/**
 * Enqueue compiled admin assets
 */
function halacoupon_enqueue_admin_assets($hook) {
    $theme_version = wp_get_theme()->get('Version');

    // =============================================================================
    // COMPILED ADMIN BUNDLES (Laravel Mix)
    // =============================================================================

    // Enqueue compiled admin CSS bundle
    wp_enqueue_style('halacoupon-admin', get_template_directory_uri() . '/assets/dist/css/admin.css', [], $theme_version);

    // Enqueue compiled admin JavaScript bundle
    wp_enqueue_script('halacoupon-admin', get_template_directory_uri() . '/assets/dist/js/admin.js', ['jquery'], $theme_version, true);

    // Localize admin script with necessary data
    wp_localize_script('halacoupon-admin', 'halacouponAdmin', [
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('halacoupon_admin_nonce'),
        'strings' => [
            'loading' => __('Loading...', 'halacoupon'),
            'error' => __('An error occurred. Please try again.', 'halacoupon'),
            'success' => __('Operation completed successfully.', 'halacoupon'),
            'confirm' => __('Are you sure?', 'halacoupon'),
        ]
    ]);
}
add_action('admin_enqueue_scripts', 'halacoupon_enqueue_admin_assets');



function halacoupon_minify_css($css) {
    $css = preg_replace('!/\*.*?\*/!s', '', $css);
    $css = preg_replace('/\n\s*\n/', '', $css);
    $css = preg_replace('/[\n\r \t]/', ' ', $css);
    $css = preg_replace('/ +/', ' ', $css);
    $css = preg_replace('/ ?([,:;{}]) ?/', '$1', $css);
    $css = preg_replace('/;}/', '}', $css);
    return trim($css);
}

function halacoupon_minify_js($js) {
    $js = preg_replace('/\s+/', ' ', $js);
    $js = preg_replace('/ ?([,:;{}]) ?/', '$1', $js);
    return trim($js);
}














