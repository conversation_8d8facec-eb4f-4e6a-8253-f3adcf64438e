<?php 
// /**
// * *********************************************
// * *                                           *
// * *                                           *
// * *     ▄████████    ▄██████▄     ▄████████   *
// * *    ███    ███   ███    ███   ███    ███   *
// * *    ███    ███   ███    █▀    ███    █▀    *
// * *    ███    ███  ▄███          ███          *
// * *  ▀███████████ ▀▀███ ████▄  ▀███████████   *
// * *    ███    ███   ███    ███          ███   *
// * *    ███    ███   ███    ███    ▄█    ███   *
// * *    ███    █▀    ████████▀   ▄████████▀    *
// * *                                           *
// * *    Abdullah G ********** +201001248698    *
// * *                                           *
// * *********************************************
// */



/**
 * Sanitize callback for taxonomy rating meta fields
 */
function halacoupon_sanitize_taxonomy_rating($value) {
    return floatval($value);
}

/**
 * Sanitize callback for taxonomy rating count meta fields
 */
function halacoupon_sanitize_taxonomy_rating_count($value) {
    return intval($value);
}

/**
 * Register metadata for taxonomy ratings
 */
function halacoupon_register_taxonomy_metadata() {
    register_meta('term', '_halacoupon_average_rating', array(
        'type' => 'number',
        'description' => 'Average rating for taxonomy',
        'single' => true,
        'sanitize_callback' => 'halacoupon_sanitize_taxonomy_rating',
        'auth_callback' => '__return_true',
    ));

    register_meta('term', '_halacoupon_total_ratings', array(
        'type' => 'number',
        'description' => 'Total number of ratings for taxonomy',
        'single' => true,
        'sanitize_callback' => 'halacoupon_sanitize_taxonomy_rating_count',
        'auth_callback' => '__return_true',
    ));

    register_meta('term', '_halacoupon_rating_sum', array(
        'type' => 'number',
        'description' => 'Sum of all ratings for taxonomy',
        'single' => true,
        'sanitize_callback' => 'halacoupon_sanitize_taxonomy_rating',
        'auth_callback' => '__return_true',
    ));
}
add_action('init', 'halacoupon_register_taxonomy_metadata');


function halacoupon_display_star_rating($taxonomy_id) {
    // Sanitize the taxonomy ID
    $taxonomy_id = absint($taxonomy_id);

    // Retrieve rating data with fallback values
    $average_rating = get_term_meta($taxonomy_id, 'halacoupon_average_rating', true) ?: 0;
    $total_ratings = get_term_meta($taxonomy_id, 'halacoupon_total_ratings', true) ?: 0;

    // Format average rating to one decimal place
    $average_rating = number_format($average_rating, 1);

    // Get the taxonomy term object
    $term = get_term($taxonomy_id);
    $term_name = $term ? $term->name : '';

    // Get user IP and ID
    $user_ip = sanitize_text_field($_SERVER['REMOTE_ADDR']);
    $user_id = get_current_user_id();

    // Retrieve rated IPs and users from term meta
    $rated_ips = get_term_meta($taxonomy_id, 'halacoupon_rated_ips', true) ?: [];
    $rated_users = get_term_meta($taxonomy_id, 'halacoupon_rated_users', true) ?: [];

    // Check if the user has already rated
    $has_rated = in_array($user_ip, $rated_ips) || in_array($user_id, $rated_users);
    if (!$has_rated && !$user_id) {
        $cookie_name = 'halacoupon_rated_' . $taxonomy_id;
        if (isset($_COOKIE[$cookie_name])) {
            $has_rated = true;
        }
    }

    // Output the rating section
    echo '<div class="flex items-center space-x-3 justify-center lg:justify-start">';
    echo '<div class="star-rating flex items-center"
             data-rating="' . esc_attr($average_rating) . '"
             data-taxonomy-id="' . esc_attr($taxonomy_id) . '"
             data-nonce="' . esc_attr(wp_create_nonce('halacoupon_rating_nonce')) . '"
             data-has-rated="' . ($has_rated ? 'true' : 'false') . '">
          </div>';
    echo '<div class="rating-summary text-sm text-gray-200">'
         . '<span class="font-semibold text-2xl text-yellow-600">' . esc_html($average_rating) . '</span>/5 · '
         . '<span class="font-medium">' . esc_html($total_ratings) . '</span> ' . esc_html__('ratings', 'halacoupon') . '</div>';

    // Conditionally display schema if ratings are available
    if ($total_ratings > 0 && $term) {
        echo '<div class="schema-rating" itemscope itemtype="https://schema.org/AggregateRating">';
        echo '<meta itemprop="ratingValue" content="' . esc_attr($average_rating) . '" />';
        echo '<meta itemprop="bestRating" content="5" />';
        echo '<meta itemprop="ratingCount" content="' . esc_attr($total_ratings) . '" />';
        
        // Add itemReviewed property for SEO compliance
        echo '<div itemprop="itemReviewed" itemscope itemtype="https://schema.org/Organization">';
        echo '<meta itemprop="name" content="' . esc_attr($term_name) . '" />';
        echo '</div>'; // Close itemReviewed

        echo '</div>'; // Close AggregateRating schema
    }

    echo '</div>'; // End of rating-wrapper
}





function halacoupon_inline_star_rating_script() {
    if (is_tax('coupon_store') || is_tax('coupon_category') || is_singular('review') || is_post_type_archive('review')) {
        ?>
        <style>
            /* Custom notification styling */
            .custom-notification {
                position: fixed;
                top: 80px;
                right: 20px;
                background-color: #ea580c;
                color: #fff;
                padding: 16px 20px;
                border-radius: 8px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
                opacity: 0;
                z-index: 9999;
                transition: all 0.3s ease;
                transform: translateX(100%);
                max-width: 320px;
                font-size: 14px;
                font-weight: 500;
            }

            .custom-notification.show {
                opacity: 1;
                transform: translateX(0);
            }

            .custom-notification.success {
                background-color: #10b981;
            }

            .custom-notification.error {
                background-color: #ef4444;
            }

            .custom-notification.already-rated {
                background-color: #ffffff;
                color: #374151;
                border: 1px solid #d1d5db;
            }

            /* Enhanced star rating styles */
            .star-button {
                transition: all 0.2s ease;
                border: none;
                background: none;
                padding: 2px;
            }

            .star-button:hover:not(:disabled) {
                transform: scale(1.1);
            }

            .star-button:focus {
                outline: none;
                box-shadow: 0 0 0 3px rgba(234, 88, 12, 0.3);
                border-radius: 4px;
            }

            .star-button:disabled {
                cursor: not-allowed;
                opacity: 0.7;
            }

            /* Screen reader only text */
            .sr-only {
                position: absolute;
                width: 1px;
                height: 1px;
                padding: 0;
                margin: -1px;
                overflow: hidden;
                clip: rect(0, 0, 0, 0);
                white-space: nowrap;
                border: 0;
            }

            /* RTL support for star ratings */
            .flex-row-reverse .star-button:focus {
                transform: scale(1.1) scaleX(-1);
            }

            /* Loading state */
            .star-button.loading {
                pointer-events: none;
                opacity: 0.6;
            }

            /* Rating summary animation */
            .rating-summary {
                transition: all 0.3s ease;
            }

            .rating-summary.updating {
                opacity: 0.7;
            }
        </style>

        <script type="text/javascript">
            jQuery(document).ready(function($) {
                console.log('Rating script loaded'); // Debug log
                
                // Create the notification element
                function showNotification(message, type = 'info') {
                    var $notification = $('<div class="custom-notification">' + message + '</div>');
                    
                    // Add type-specific styling
                    if (type === 'success') {
                        $notification.addClass('success');
                    } else if (type === 'error') {
                        $notification.addClass('error');
                    } else if (type === 'already_rated') {
                        $notification.addClass('already-rated');
                    }
                    
                    $('body').append($notification);

                    // Show the notification
                    setTimeout(function() {
                        $notification.addClass('show');
                    }, 100); // Slight delay for the transition effect

                    // Hide the notification after 3 seconds
                    setTimeout(function() {
                        $notification.removeClass('show');
                        setTimeout(function() {
                            $notification.remove();
                        }, 300); // Allow time for the fade-out transition before removing
                    }, 3000);
                }

                $('.star-rating').each(function() {
                    var $this = $(this);
                    var hasRated = $this.data('has-rated'); // This data is passed from PHP to disable rating

                    $this.starRating({
                        totalStars: 5,
                        starShape: 'rounded',
                        starSize: 25,
                        emptyColor: 'lightgray',
                        hoverColor: '#F5DC4D',
                        activeColor: '#F5DC4D',
                        ratedColor: '#F5DC4D',
                        useGradient: false,
                        initialRating: parseFloat($this.data('rating')).toFixed(1), // Format the initial rating to one decimal place
                        readOnly: hasRated, // Set readOnly if the user has already rated
                        callback: function(currentRating, $el) {
                            if (hasRated) {
                                // If already rated, show a notification
                                showNotification('<?php echo __('You have already rated this store today.', 'halacoupon'); ?>');
                                return; // Prevent additional ratings if already rated
                            }

                            var taxonomyId = $el.data('taxonomy-id');
                            $.ajax({
                                url: halacouponRating.ajax_url,
                                type: 'POST',
                                data: {
                                    action: 'halacoupon_save_rating',
                                    nonce: halacouponRating.nonce,
                                    taxonomy_id: taxonomyId,
                                    rating: currentRating
                                },
                                success: function(response) {
                                    console.log('AJAX Response:', response); // Debug log
                                    
                                    if (response.success) {
                                        var roundedRating = parseFloat(response.data.new_rating).toFixed(1); // Format to one decimal place
                                        $el.siblings('.rating-summary').text(roundedRating + '/5 - ' + response.data.total_ratings + ' <?php echo __('total ratings', 'halacoupon'); ?>');
                                        $el.starRating('setReadOnly', true); // Make stars read-only after rating
                                    } else {
                                        showNotification(response.data.message);
                                    }
                                },
                                error: function() {
                                    showNotification('<?php echo __('Error submitting rating. Please try again.', 'halacoupon'); ?>');
                                }
                            });
                        }
                    });
                });

                // Article rating stars
                $('.article-star-rating').each(function() {
                    var $container = $(this);
                    var $stars = $container.find('.star-button');
                    var hasRated = $container.data('has-rated') === 'true';
                    var currentRating = parseFloat($container.data('rating')) || 0;
                    var isProcessing = false; // Prevent double-clicks

                    // Initialize star states
                    function updateStarStates(rating) {
                        $stars.each(function(index) {
                            var starIndex = index + 1;
                            var $star = $(this);
                            var $svg = $star.find('svg');
                            
                            if (starIndex <= rating) {
                                $star.removeClass('text-gray-300').addClass('text-primary');
                                $star.attr('aria-checked', 'true');
                            } else {
                                $star.removeClass('text-primary').addClass('text-gray-300');
                                $star.attr('aria-checked', 'false');
                            }
                        });
                    }

                    // Initialize with current rating
                    updateStarStates(currentRating);

                    // Hover effects
                    $stars.on('mouseenter', function() {
                        if (hasRated || isProcessing) return;
                        var hoverRating = $(this).data('rating');
                        updateStarStates(hoverRating);
                    });

                    $container.on('mouseleave', function() {
                        if (hasRated || isProcessing) return;
                        updateStarStates(currentRating);
                    });

                    // Star click handler
                    $stars.on('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        
                        // Prevent double-clicks and already rated clicks
                        if (isProcessing || hasRated) {
                            if (hasRated) {
                                showNotification('<?php echo __('You have already rated this article.', 'halacoupon'); ?>', 'already_rated');
                            }
                            return;
                        }

                        var rating = $(this).data('rating');
                        var articleId = $container.data('article-id');
                        var nonce = $container.data('nonce');

                        // Set processing flag immediately
                        isProcessing = true;

                        // Send AJAX request FIRST, before updating visual state
                        $.ajax({
                            url: (typeof halacouponRating !== 'undefined' && halacouponRating.ajax_url) ? halacouponRating.ajax_url : '<?php echo admin_url('admin-ajax.php'); ?>',
                            type: 'POST',
                            data: {
                                action: 'halacoupon_save_article_rating',
                                nonce: nonce,
                                article_id: articleId,
                                rating: rating
                            },
                            success: function(response) {
                                console.log('AJAX Response:', response); // Debug log
                                
                                if (response.success) {
                                    // Update visual state only on success
                                    currentRating = rating;
                                    updateStarStates(rating);
                                    hasRated = true;
                                    $container.data('has-rated', 'true');

                                    // Disable all stars
                                    $stars.prop('disabled', true).attr('aria-disabled', 'true');

                                    // Update rating summary
                                    var $summary = $container.siblings('.rating-summary');
                                    $summary.html('<span class="font-semibold text-primary">' + response.data.new_rating + '</span>/5 · <span class="font-medium">' + response.data.total_ratings + '</span> <?php echo __('ratings', 'halacoupon'); ?>');
                                    
                                    console.log('Showing success notification:', response.data.message); // Debug log
                                    showNotification(response.data.message || 'Thank you for rating this article!', 'success');
                                    
                                    // Update schema if it exists
                                    if (response.data.total_ratings > 0) {
                                        // Trigger schema update
                                        $(document).trigger('rating_updated', {
                                            article_id: articleId,
                                            new_rating: response.data.new_rating,
                                            total_ratings: response.data.total_ratings
                                        });
                                    }
                                } else {
                                    // Handle error - don't update visual state
                                    isProcessing = false;
                                    
                                    console.log('Showing error notification:', response.data.message); // Debug log
                                    // Show appropriate notification based on error type
                                    var notificationType = response.data.type || 'error';
                                    showNotification(response.data.message, notificationType);
                                    
                                    // If it's an "already rated" error, mark as rated
                                    if (response.data.type === 'already_rated') {
                                        hasRated = true;
                                        $container.data('has-rated', 'true');
                                        $stars.prop('disabled', true).attr('aria-disabled', 'true');
                                    }
                                }
                            },
                            error: function(xhr, status, error) {
                                // Handle network error - don't update visual state
                                isProcessing = false;
                                showNotification('<?php echo __('Error submitting rating. Please try again.', 'halacoupon'); ?>', 'error');
                                console.log('AJAX Error:', xhr.responseText);
                            }
                        });
                    });

                    // Keyboard navigation for accessibility
                    $stars.on('keydown', function(e) {
                        if (hasRated) return;
                        
                        var $currentStar = $(this);
                        var currentIndex = $stars.index($currentStar);
                        
                        switch(e.keyCode) {
                            case 37: // Left arrow
                                e.preventDefault();
                                if (currentIndex > 0) {
                                    $stars.eq(currentIndex - 1).focus();
                                }
                                break;
                            case 39: // Right arrow
                                e.preventDefault();
                                if (currentIndex < $stars.length - 1) {
                                    $stars.eq(currentIndex + 1).focus();
                                }
                                break;
                            case 13: // Enter
                            case 32: // Space
                                e.preventDefault();
                                $currentStar.click();
                                break;
                        }
                    });
                });
            });
        </script>
        <?php
    }
}
add_action('wp_footer', 'halacoupon_inline_star_rating_script');

function halacoupon_save_rating() {
    check_ajax_referer('halacoupon_rating_nonce', 'nonce');

    $taxonomy_id = isset($_POST['taxonomy_id']) ? intval($_POST['taxonomy_id']) : 0;
    $rating = isset($_POST['rating']) ? floatval($_POST['rating']) : 0;

    if ($taxonomy_id <= 0 || $rating <= 0 || $rating > 5) {
        wp_send_json_error(array('message' => __('Invalid rating data.', 'halacoupon')));
    }

    // Get existing rating data
    $total_ratings = get_term_meta($taxonomy_id, 'halacoupon_total_ratings', true) ?: 0;
    $current_rating_sum = get_term_meta($taxonomy_id, 'halacoupon_rating_sum', true) ?: 0;
    
    // Ensure that rated_ips and rated_users are arrays
    $rated_ips = maybe_unserialize(get_term_meta($taxonomy_id, 'halacoupon_rated_ips', true)) ?: array();
    $rated_users = maybe_unserialize(get_term_meta($taxonomy_id, 'halacoupon_rated_users', true)) ?: array();

    $user_ip = $_SERVER['REMOTE_ADDR'];
    $user_id = get_current_user_id();

    // Check if user has already rated
    if (in_array($user_ip, $rated_ips) || in_array($user_id, $rated_users)) {
        wp_send_json_error(array('message' => __('You have already rated this store.', 'halacoupon')));
    }

    // Update rating data
    $new_rating_sum = $current_rating_sum + $rating;
    $new_total_ratings = $total_ratings + 1;
    $new_average_rating = $new_rating_sum / $new_total_ratings;

    // Add IP and user ID to the list of rated IPs/users
    $rated_ips[$user_ip] = current_time('timestamp');
    if ($user_id) {
        $rated_users[] = $user_id;
    }

    update_term_meta($taxonomy_id, 'halacoupon_total_ratings', $new_total_ratings);
    update_term_meta($taxonomy_id, 'halacoupon_rating_sum', $new_rating_sum);
    update_term_meta($taxonomy_id, 'halacoupon_average_rating', $new_average_rating);
    update_term_meta($taxonomy_id, 'halacoupon_rated_ips', $rated_ips);
    update_term_meta($taxonomy_id, 'halacoupon_rated_users', $rated_users);

    wp_send_json_success(array(
        'new_rating' => round($new_average_rating, 1),
        'total_ratings' => $new_total_ratings,
        'message' => __('Thank you for rating this article!', 'halacoupon')
    ));
}

// Hook into WordPress to handle AJAX requests
add_action('wp_ajax_halacoupon_save_rating', 'halacoupon_save_rating');
add_action('wp_ajax_nopriv_halacoupon_save_rating', 'halacoupon_save_rating');

/**
 * Review Articles Rating System
 *
 * Rating functionality for Review Articles custom post type
 * Users can rate the review articles themselves, not the products/stores
 */

/**
 * Sanitize callback for article rating meta fields
 */
function halacoupon_sanitize_article_rating($value) {
    return floatval($value);
}

/**
 * Sanitize callback for article rating count meta fields
 */
function halacoupon_sanitize_article_rating_count($value) {
    return intval($value);
}

/**
 * Register metadata for review articles
 */
function halacoupon_register_review_metadata() {
    // Average rating
    register_meta('post', '_halacoupon_article_average_rating', array(
        'type' => 'number',
        'description' => 'Average rating for this review article',
        'single' => true,
        'sanitize_callback' => 'halacoupon_sanitize_article_rating',
        'auth_callback' => '__return_true',
        'show_in_rest' => true,
    ));

    // Total ratings count
    register_meta('post', '_halacoupon_article_total_ratings', array(
        'type' => 'number',
        'description' => 'Total number of ratings for this review article',
        'single' => true,
        'sanitize_callback' => 'halacoupon_sanitize_article_rating_count',
        'auth_callback' => '__return_true',
        'show_in_rest' => true,
    ));

    // Rating sum for calculations
    register_meta('post', '_halacoupon_article_rating_sum', array(
        'type' => 'number',
        'description' => 'Sum of all ratings for this review article',
        'single' => true,
        'sanitize_callback' => 'halacoupon_sanitize_article_rating',
        'auth_callback' => '__return_true',
        'show_in_rest' => false,
    ));

    // Rated users/IPs tracking - SAME AS STORES
    register_meta('post', '_halacoupon_article_rated_ips', array(
        'type' => 'string',
        'description' => 'Serialized array of IPs that rated this article',
        'single' => true,
        'sanitize_callback' => 'sanitize_text_field',
        'auth_callback' => '__return_true',
        'show_in_rest' => false,
    ));

    register_meta('post', '_halacoupon_article_rated_users', array(
        'type' => 'string',
        'description' => 'Serialized array of user IDs that rated this article',
        'single' => true,
        'sanitize_callback' => 'sanitize_text_field',
        'auth_callback' => '__return_true',
        'show_in_rest' => false,
    ));
}
add_action('init', 'halacoupon_register_review_metadata');

/**
 * AJAX handler for saving article ratings - following stores approach
 */
function halacoupon_save_article_rating() {
    check_ajax_referer('halacoupon_rating_nonce', 'nonce');

    $article_id = isset($_POST['article_id']) ? intval($_POST['article_id']) : 0;
    $rating = isset($_POST['rating']) ? floatval($_POST['rating']) : 0;

    if ($article_id <= 0 || $rating <= 0 || $rating > 5) {
        wp_send_json_error(array('message' => __('Invalid rating data.', 'halacoupon')));
    }

    // Verify it's a review article
    if (get_post_type($article_id) !== 'review') {
        wp_send_json_error(array('message' => __('Invalid article.', 'halacoupon')));
    }

    $user_ip = $_SERVER['REMOTE_ADDR'];
    $user_id = get_current_user_id();

    // CRITICAL: Check if user has already rated BEFORE getting any other data
    $rated_ips_json = get_post_meta($article_id, '_halacoupon_article_rated_ips', true);
    $rated_users_json = get_post_meta($article_id, '_halacoupon_article_rated_users', true);
    
    // Decode JSON arrays, fallback to empty array if invalid
    $rated_ips = !empty($rated_ips_json) ? json_decode($rated_ips_json, true) : array();
    $rated_users = !empty($rated_users_json) ? json_decode($rated_users_json, true) : array();
    
    // Ensure they are arrays
    if (!is_array($rated_ips)) $rated_ips = array();
    if (!is_array($rated_users)) $rated_users = array();

    // Check if user has already rated - with 24-hour reset for IPs
    $current_time = current_time('timestamp');
    $twenty_four_hours_ago = $current_time - (24 * 60 * 60);
    
    // Check for logged-in users (no time limit)
    if ($user_id && in_array($user_id, $rated_users)) {
        wp_send_json_error(array(
            'message' => __('You have already rated this article.', 'halacoupon'),
            'type' => 'already_rated'
        ));
    }
    
    // Check for IP addresses with 24-hour limit
    if (isset($rated_ips[$user_ip])) {
        $last_rating_time = $rated_ips[$user_ip];
        if ($last_rating_time > $twenty_four_hours_ago) {
            wp_send_json_error(array(
                'message' => __('You can rate this article again in 24 hours.', 'halacoupon'),
                'type' => 'already_rated'
            ));
        }
    }

    // Only get rating data AFTER confirming user hasn't rated
    $total_ratings = get_post_meta($article_id, '_halacoupon_article_total_ratings', true) ?: 0;
    $current_rating_sum = get_post_meta($article_id, '_halacoupon_article_rating_sum', true) ?: 0;

    // Update rating data
    $new_rating_sum = $current_rating_sum + $rating;
    $new_total_ratings = $total_ratings + 1;
    $new_average_rating = $new_rating_sum / $new_total_ratings;

    // Add IP and user ID to the list of rated IPs/users - SAME AS STORES
    $rated_ips[$user_ip] = $current_time;
    if ($user_id) {
        $rated_users[] = $user_id;
    }

    // Clean up old IP entries (older than 24 hours)
    $rated_ips = array_filter($rated_ips, function($timestamp) use ($twenty_four_hours_ago) {
        return $timestamp > $twenty_four_hours_ago;
    });

    // Set cookie for non-logged-in users (24 hours)
    if (!$user_id) {
        setcookie('halacoupon_article_rated_' . $article_id, $current_time, time() + (24 * 60 * 60), '/', '', is_ssl(), true);
    }

    // Try to update meta fields - use add_post_meta as fallback if update fails
    $update_result1 = update_post_meta($article_id, '_halacoupon_article_total_ratings', $new_total_ratings);
    if ($update_result1 === false) {
        $update_result1 = add_post_meta($article_id, '_halacoupon_article_total_ratings', $new_total_ratings, true);
    }
    
    $update_result2 = update_post_meta($article_id, '_halacoupon_article_rating_sum', $new_rating_sum);
    if ($update_result2 === false) {
        $update_result2 = add_post_meta($article_id, '_halacoupon_article_rating_sum', $new_rating_sum, true);
    }
    
    $update_result3 = update_post_meta($article_id, '_halacoupon_article_average_rating', $new_average_rating);
    if ($update_result3 === false) {
        $update_result3 = add_post_meta($article_id, '_halacoupon_article_average_rating', $new_average_rating, true);
    }
    
    // Store arrays as JSON strings
    $rated_ips_json = json_encode($rated_ips);
    $rated_users_json = json_encode($rated_users);
    
    $update_result4 = update_post_meta($article_id, '_halacoupon_article_rated_ips', $rated_ips_json);
    if ($update_result4 === false) {
        $update_result4 = add_post_meta($article_id, '_halacoupon_article_rated_ips', $rated_ips_json, true);
    }
    
    $update_result5 = update_post_meta($article_id, '_halacoupon_article_rated_users', $rated_users_json);
    if ($update_result5 === false) {
        $update_result5 = add_post_meta($article_id, '_halacoupon_article_rated_users', $rated_users_json, true);
    }

    // Debug logging
    if (WP_DEBUG) {
        error_log("=== Article Rating Debug ===");
        error_log("Article ID: $article_id");
        error_log("User IP: $user_ip");
        error_log("User ID: $user_id");
        error_log("Rating: $rating");
        error_log("Update Results:");
        error_log("  Total Ratings: " . ($update_result1 ? 'SUCCESS' : 'FAILED'));
        error_log("  Rating Sum: " . ($update_result2 ? 'SUCCESS' : 'FAILED'));
        error_log("  Average Rating: " . ($update_result3 ? 'SUCCESS' : 'FAILED'));
        error_log("  Rated IPs: " . ($update_result4 ? 'SUCCESS' : 'FAILED'));
        error_log("  Rated Users: " . ($update_result5 ? 'SUCCESS' : 'FAILED'));
        error_log("Rated IPs Array: " . print_r($rated_ips, true));
        error_log("Rated Users Array: " . print_r($rated_users, true));
        error_log("=== End Debug ===");
    }

    // Check if all updates were successful
    if ($update_result1 === false || $update_result2 === false || $update_result3 === false || $update_result4 === false || $update_result5 === false) {
        $error_details = array();
        if ($update_result1 === false) $error_details[] = 'total_ratings';
        if ($update_result2 === false) $error_details[] = 'rating_sum';
        if ($update_result3 === false) $error_details[] = 'average_rating';
        if ($update_result4 === false) $error_details[] = 'rated_ips';
        if ($update_result5 === false) $error_details[] = 'rated_users';
        
        if (WP_DEBUG) {
            error_log("Failed updates: " . implode(', ', $error_details));
        }
        
        wp_send_json_error(array(
            'message' => __('Failed to save rating data.', 'halacoupon'),
            'debug' => WP_DEBUG ? 'Failed fields: ' . implode(', ', $error_details) : null
        ));
    }

    wp_send_json_success(array(
        'new_rating' => round($new_average_rating, 1),
        'total_ratings' => $new_total_ratings,
        'message' => __('Thank you for rating this article!', 'halacoupon')
    ));
}

/**
 * Display star rating for review articles (user ratings of the article)
 *
 * @param int $article_id Review article post ID
 * @param bool $interactive Whether the rating should be interactive
 * @return void
 */
function halacoupon_display_article_rating($article_id, $interactive = true) {
    $article_id = absint($article_id);

    // Get article rating data
    $average_rating = get_post_meta($article_id, '_halacoupon_article_average_rating', true) ?: 0;
    $total_ratings = get_post_meta($article_id, '_halacoupon_article_total_ratings', true) ?: 0;
    $average_rating = floatval($average_rating);

    // Check if user has already rated - SAME AS STORES
    $user_ip = $_SERVER['REMOTE_ADDR'];
    $user_id = get_current_user_id();

    $rated_ips_json = get_post_meta($article_id, '_halacoupon_article_rated_ips', true);
    $rated_users_json = get_post_meta($article_id, '_halacoupon_article_rated_users', true);
    
    // Decode JSON arrays, fallback to empty array if invalid
    $rated_ips = !empty($rated_ips_json) ? json_decode($rated_ips_json, true) : array();
    $rated_users = !empty($rated_users_json) ? json_decode($rated_users_json, true) : array();
    
    // Ensure they are arrays
    if (!is_array($rated_ips)) $rated_ips = array();
    if (!is_array($rated_users)) $rated_users = array();

    // Check if user has already rated - with 24-hour reset for IPs
    $current_time = current_time('timestamp');
    $twenty_four_hours_ago = $current_time - (24 * 60 * 60);
    
    $has_rated = false;
    
    // Check for logged-in users (no time limit)
    if ($user_id && in_array($user_id, $rated_users)) {
        $has_rated = true;
    }
    
    // Check for IP addresses with 24-hour limit
    if (!$has_rated && isset($rated_ips[$user_ip])) {
        $last_rating_time = $rated_ips[$user_ip];
        if ($last_rating_time > $twenty_four_hours_ago) {
            $has_rated = true;
        }
    }
    
    // Check cookie for non-logged-in users (24-hour limit)
    if (!$has_rated && !$user_id) {
        $cookie_name = 'halacoupon_article_rated_' . $article_id;
        if (isset($_COOKIE[$cookie_name])) {
            $cookie_time = intval($_COOKIE[$cookie_name]);
            if ($cookie_time > $twenty_four_hours_ago) {
                $has_rated = true;
            }
        }
    }

    $is_rtl = is_rtl();
    $star_direction = $is_rtl ? 'flex-row-reverse' : '';

    echo '<div class="article-rating-display flex items-center gap-3">';

    if ($interactive && !$has_rated) {
        // Interactive rating stars
        echo '<div class="article-star-rating flex items-center gap-1 ' . $star_direction . '"
                 data-rating="' . esc_attr($average_rating) . '"
                 data-article-id="' . esc_attr($article_id) . '"
                 data-nonce="' . esc_attr(wp_create_nonce('halacoupon_rating_nonce')) . '"
                 data-has-rated="' . ($has_rated ? 'true' : 'false') . '">';
        for ($i = 1; $i <= 5; $i++) {
            $star_class = $i <= $average_rating ? 'text-primary' : 'text-gray-300';
            echo '<button type="button" 
                         class="star-button w-5 h-5 ' . $star_class . ' cursor-pointer hover:text-primary transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary/50 rounded"
                         data-rating="' . $i . '"
                         aria-label="' . sprintf(__('%d star%s', 'halacoupon'), $i, $i === 1 ? '' : 's') . '"
                         role="radio"
                         aria-checked="' . ($i <= $average_rating ? 'true' : 'false') . '"
                         tabindex="0">';
            echo '<svg class="w-full h-full" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">';
            echo '<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>';
            echo '</svg>';
            echo '</button>';
        }
        echo '</div>';
    } else {
        // Static rating display
        echo '<div class="stars-display flex items-center gap-1 ' . $star_direction . '">';
        for ($i = 1; $i <= 5; $i++) {
            $star_class = $i <= $average_rating ? 'text-primary' : 'text-gray-300';
            echo '<svg class="w-5 h-5 ' . $star_class . '" fill="currentColor" viewBox="0 0 20 20">';
            echo '<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>';
            echo '</svg>';
        }
        echo '</div>';
    }

    // Rating summary
    echo '<div class="rating-summary text-sm text-gray-200">';
    if ($total_ratings > 0) {
        echo '<span class="font-semibold text-primary">' . number_format($average_rating, 1) . '</span>/5 · ';
        echo '<span class="font-medium">' . esc_html($total_ratings) . '</span> ';
        echo esc_html(_n('rating', 'ratings', $total_ratings, 'halacoupon'));
    } else {
        echo '<span class="text-gray-500">' . esc_html__('No ratings yet', 'halacoupon') . '</span>';
    }
    echo '</div>';

    echo '</div>';

    // Schema markup for article rating
    if ($total_ratings > 0) {
        echo '<div class="article-rating-schema" itemscope itemtype="https://schema.org/AggregateRating" style="display: none;">';
        echo '<meta itemprop="ratingValue" content="' . esc_attr($average_rating) . '">';
        echo '<meta itemprop="bestRating" content="5">';
        echo '<meta itemprop="ratingCount" content="' . esc_attr($total_ratings) . '">';
        echo '<div itemprop="itemReviewed" itemscope itemtype="https://schema.org/Article">';
        echo '<meta itemprop="name" content="' . esc_attr(get_the_title($article_id)) . '">';
        echo '</div>';
        echo '</div>';
    }
}

// Hook into WordPress to handle AJAX requests for article ratings
add_action('wp_ajax_halacoupon_save_article_rating', 'halacoupon_save_article_rating');
add_action('wp_ajax_nopriv_halacoupon_save_article_rating', 'halacoupon_save_article_rating');