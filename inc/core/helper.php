<?php
// /**
// * *********************************************
// * *                                           *
// * *                                           *
// * *     ▄████████    ▄██████▄     ▄████████   *
// * *    ███    ███   ███    ███   ███    ███   *
// * *    ███    ███   ███    █▀    ███    █▀    *
// * *    ███    ███  ▄███          ███          *
// * *  ▀███████████ ▀▀███ ████▄  ▀███████████   *
// * *    ███    ███   ███    ███          ███   *
// * *    ███    ███   ███    ███    ▄█    ███   *
// * *    ███    █▀    ████████▀   ▄████████▀    *
// * *                                           *
// * *    Abdullah G ********** +201001248698    *
// * *                                           *
// * *********************************************
// */





function halacoupon_remove_class_filter( $tag, $class_name = '', $method_name = '', $priority = 10 ) {
	global $wp_filter;

	// Check that filter actually exists first
	if ( ! isset( $wp_filter[ $tag ] ) ) {
		return false;
	}

	/**
	 * If filter config is an object, means we're using WordPress 4.7+ and the config is no longer
	 * a simple array, rather it is an object that implements the ArrayAccess interface.
	 *
	 * To be backwards compatible, we set $callbacks equal to the correct array as a reference (so $wp_filter is updated)
	 *
	 * @see https://make.wordpress.org/core/2016/09/08/wp_hook-next-generation-actions-and-filters/
	 */
	if ( is_object( $wp_filter[ $tag ] ) && isset( $wp_filter[ $tag ]->callbacks ) ) {
		// Create $fob object from filter tag, to use below
		$fob = $wp_filter[ $tag ];
		$callbacks = &$wp_filter[ $tag ]->callbacks;
	} else {
		$callbacks = &$wp_filter[ $tag ];
	}

	// Exit if there aren't any callbacks for specified priority
	if ( ! isset( $callbacks[ $priority ] ) || empty( $callbacks[ $priority ] ) ) {
		return false;
	}

	// Loop through each filter for the specified priority, looking for our class & method
	foreach ( (array) $callbacks[ $priority ] as $filter_id => $filter ) {

		// Filter should always be an array - array( $this, 'method' ), if not goto next
		if ( ! isset( $filter['function'] ) || ! is_array( $filter['function'] ) ) {
			continue;
		}

		// If first value in array is not an object, it can't be a class
		if ( ! is_object( $filter['function'][0] ) ) {
			continue;
		}

		// Method doesn't match the one we're looking for, goto next
		if ( $filter['function'][1] !== $method_name ) {
			continue;
		}

		// Method matched, now let's check the Class
		if ( get_class( $filter['function'][0] ) === $class_name ) {

			// WordPress 4.7+ use core remove_filter() since we found the class object
			if ( isset( $fob ) ) {
				// Handles removing filter, reseting callback priority keys mid-iteration, etc.
				$fob->remove_filter( $tag, $filter['function'], $priority );

			} else {
				// Use legacy removal process (pre 4.7)
				unset( $callbacks[ $priority ][ $filter_id ] );
				// and if it was the only filter in that priority, unset that priority
				if ( empty( $callbacks[ $priority ] ) ) {
					unset( $callbacks[ $priority ] );
				}
				// and if the only filter for that tag, set the tag to an empty array
				if ( empty( $callbacks ) ) {
					$callbacks = array();
				}
				// Remove this filter from merged_filters, which specifies if filters have been sorted
				unset( $GLOBALS['merged_filters'][ $tag ] );
			}

			return true;
		}
	}

	return false;
}

/**
 * Remove Class Action Without Access to Class Object
 *
 * In order to use the core WordPress remove_action() on an action added with the callback
 * to a class, you either have to have access to that class object, or it has to be a call
 * to a static method.  This method allows you to remove actions with a callback to a class
 * you don't have access to.
 *
 * Works with WordPress 1.2+ (4.7+ support added 9-19-2016)
 *
 * @param string $tag         Action to remove
 * @param string $class_name  Class name for the action's callback
 * @param string $method_name Method name for the action's callback
 * @param int    $priority    Priority of the action (default 10)
 *
 * @return bool               Whether the function is removed.
 */
function halacoupon_remove_class_action( $tag, $class_name = '', $method_name = '', $priority = 10 ) {
	halacoupon_remove_class_filter( $tag, $class_name, $method_name, $priority );
}


add_action( 'init', 'flush_rewrite_rules' );


/**
 * Add rewrite for go out store
 *
 * domain-name.com/out/123
 *
 * @since 1.0.0
 */
function halacoupon_add_rewrite_rules() {
	// Blog post feed links
	add_rewrite_rule( '^blog/(feed|rdf|rss|rss2|atom)/?$', 'index.php?feed=feed', 'top' );
	add_rewrite_rule( '^blog/feed/(feed|rdf|rss|rss2|atom)/?$', 'index.php?feed=$matches[1]', 'top' );

	// Blog post single pages with /blog/ prefix
	// Pattern: blog/post-name/ or blog/post-name/page/2/ (for paginated posts)
	add_rewrite_rule( '^blog/([^/]+)/page/?([0-9]{1,})/?$', 'index.php?name=$matches[1]&paged=$matches[2]', 'top' );
	add_rewrite_rule( '^blog/([^/]+)/?$', 'index.php?name=$matches[1]', 'top' );

	// Blog post comments feed
	add_rewrite_rule( '^blog/([^/]+)/(feed|rdf|rss|rss2|atom)/?$', 'index.php?name=$matches[1]&feed=$matches[2]', 'top' );
	add_rewrite_rule( '^blog/([^/]+)/feed/(feed|rdf|rss|rss2|atom)/?$', 'index.php?name=$matches[1]&feed=$matches[2]', 'top' );

	// Blog archive pages with /blog/ prefix
	add_rewrite_rule( '^blog/page/?([0-9]{1,})/?$', 'index.php?paged=$matches[1]', 'top' );

	// Blog category and tag archives with /blog/ prefix
	$category_base = get_option( 'category_base' ) ?: 'category';
	$tag_base = get_option( 'tag_base' ) ?: 'tag';

	// Blog category archives: /blog/category/category-name/ and /blog/category/category-name/page/2/
	add_rewrite_rule( '^blog/' . $category_base . '/([^/]+)/page/?([0-9]{1,})/?$', 'index.php?category_name=$matches[1]&paged=$matches[2]', 'top' );
	add_rewrite_rule( '^blog/' . $category_base . '/([^/]+)/?$', 'index.php?category_name=$matches[1]', 'top' );

	// Blog tag archives: /blog/tag/tag-name/ and /blog/tag/tag-name/page/2/
	add_rewrite_rule( '^blog/' . $tag_base . '/([^/]+)/page/?([0-9]{1,})/?$', 'index.php?tag=$matches[1]&paged=$matches[2]', 'top' );
	add_rewrite_rule( '^blog/' . $tag_base . '/([^/]+)/?$', 'index.php?tag=$matches[1]', 'top' );

	// Blog date archives with /blog/ prefix
	add_rewrite_rule( '^blog/([0-9]{4})/([0-9]{1,2})/([0-9]{1,2})/page/?([0-9]{1,})/?$', 'index.php?year=$matches[1]&monthnum=$matches[2]&day=$matches[3]&paged=$matches[4]', 'top' );
	add_rewrite_rule( '^blog/([0-9]{4})/([0-9]{1,2})/([0-9]{1,2})/?$', 'index.php?year=$matches[1]&monthnum=$matches[2]&day=$matches[3]', 'top' );
	add_rewrite_rule( '^blog/([0-9]{4})/([0-9]{1,2})/page/?([0-9]{1,})/?$', 'index.php?year=$matches[1]&monthnum=$matches[2]&paged=$matches[3]', 'top' );
	add_rewrite_rule( '^blog/([0-9]{4})/([0-9]{1,2})/?$', 'index.php?year=$matches[1]&monthnum=$matches[2]', 'top' );
	add_rewrite_rule( '^blog/([0-9]{4})/page/?([0-9]{1,})/?$', 'index.php?year=$matches[1]&paged=$matches[2]', 'top' );
	add_rewrite_rule( '^blog/([0-9]{4})/?$', 'index.php?year=$matches[1]', 'top' );

	// Blog author archives with /blog/ prefix
	add_rewrite_rule( '^blog/author/([^/]+)/page/?([0-9]{1,})/?$', 'index.php?author_name=$matches[1]&paged=$matches[2]', 'top' );
	add_rewrite_rule( '^blog/author/([^/]+)/?$', 'index.php?author_name=$matches[1]', 'top' );

	// Change default feed link to feed coupons
	add_rewrite_rule( '^(feed|rdf|rss|rss2|atom)/?$', 'index.php?home_feed=coupon&feed=$matches[1]', 'top' );

	// Redirect to coupon site
	$slug = halacoupon_get_option( 'go_out_slug', 'out' );
	add_rewrite_rule( '^' . $slug . '/([0-9]+)/?', 'index.php?out=$matches[1]', 'top' );
	// Go to store redirect
	$slug = halacoupon_get_option( 'go_store_slug', 'go-store' );
	add_rewrite_rule( '^' . $slug . '/([0-9]+)/?', 'index.php?go_store_id=$matches[1]', 'top' );

	$store_slug = trim( halacoupon_get_option( 'rewrite_store_slug', '' ) );
	if ( ! $store_slug ) {
		$store_slug = 'discount-codes';
	}

	// [discount-codes/([^/]+)/page/?([0-9]{1,})/?$] => index.php?coupon_store=$matches[1]&paged=$matches[2]
	// [discount-codes/([^/]+)/?$] => index.php?coupon_store=$matches[1]
	// Store with page number in url
	add_rewrite_rule( '^' . $store_slug . '/([^/]+)/page/?([0-9]{1,})/?', 'index.php?coupon_store=$matches[1]&paged=$matches[2]', 'top' );

	// Store Feed link
	add_rewrite_rule( '^' . $store_slug . '/([^/]+)/(feed|rdf|rss|rss2|atom)/?', 'index.php?coupon_store=$matches[1]&feed=$matches[1]', 'top' );

	// Share url
	add_rewrite_rule( '^' . $store_slug . '/([^/]+)/share/([0-9]+)/?', 'index.php?coupon_store=$matches[1]&share_id=$matches[2]', 'top' );

	if ( halacoupon_get_option( 'enable_single_coupon', false ) ) {
		// Single coupon link
		add_rewrite_rule( '^' . $store_slug . '/([^/]+)/([^/]+)/?', 'index.php?coupon=$matches[2]', 'top' );
	} else {
		// Open coupon modal
		add_rewrite_rule( '^' . $store_slug . '/([^/]+)/([^/]+)/?', 'index.php?coupon_store=$matches[1]&coupon_id=$matches[2]', 'top' );
	}

}

/**
 * Add new query vars
 *
 * @see get_query_var()
 * @since 1.0.0
 */
function halacoupon_rewrite_tags() {
	add_rewrite_tag( '%home_feed%', '([^&]+)' );
	add_rewrite_tag( '%out%', '([^&]+)' );
	add_rewrite_tag( '%go_store_id%', '([^&]+)' );
	add_rewrite_tag( '%share_id%', '([^&]+)' );
	add_rewrite_tag( '%coupon_id%', '([^&]+)' );
}

/**
 * Init rewrite setup
 */
add_action( 'init', 'halacoupon_add_rewrite_rules', 11, 0 );
add_action( 'init', 'halacoupon_rewrite_tags', 11, 0 );

/**
 * Flush rewrite rules when store slug is changed
 */
function halacoupon_flush_rewrite_on_store_slug_change() {
	// Check if we're in admin and the store slug option was updated
	if ( is_admin() && isset( $_POST['rewrite_store_slug'] ) ) {
		$old_slug = halacoupon_get_option( 'rewrite_store_slug', 'discount-codes' );
		$new_slug = sanitize_text_field( $_POST['rewrite_store_slug'] );

		if ( $old_slug !== $new_slug ) {
			// Flush rewrite rules to regenerate with new slug
			flush_rewrite_rules();
		}
	}
}
add_action( 'admin_init', 'halacoupon_flush_rewrite_on_store_slug_change' );


add_action( 'init', 'halacoupon_request_uri_setup' );
/**
 * Do set up with request uri
 */
function halacoupon_request_uri_setup() {
	$GLOBALS['st_paged'] = 0;
	global $wp_rewrite;
	$matches = false;
	if ( $wp_rewrite->using_permalinks() ) {
		preg_match( '/page\/([0-9]+)/', $_SERVER['REQUEST_URI'], $matches );
	} else {
		preg_match( '/paged=([0-9]+)/', $_SERVER['REQUEST_URI'], $matches );
	}
	if ( $matches ) {
		$GLOBALS['st_paged'] = $matches[1];
	}

	if ( preg_match( '/(feed|rdf|rss|rss2|atom)/', $_SERVER['REQUEST_URI'], $matches_2 ) ) {

	}

}



/**
 * Get paged number
 */
function halacoupon_get_paged() {
	global $paged;
	if ( ! $paged ) {
		return intval( $GLOBALS['st_paged'] ) > 0 ? intval( $GLOBALS['st_paged'] ) : 1;
	}

	return $paged;
}


/**
 * Get registered_sidebars for setting options
 */
function halacoupon_get_registered_sidebars() {
	global $wp_registered_sidebars;

	// st_debug( $wp_registered_sidebars );
	$a = array();
	foreach ( $wp_registered_sidebars as $k => $s ) {
		$a[ $k ] = $s['name'];
	}
	return $a;
}


function halacoupon_change_coupon_feed_link( $link ) {
	$post = get_post();
	// if not a coupon return the link
	if ( get_post_type( $post ) != 'coupon' ) {
		return $link;
	}
	$link = halacoupon_coupon( $post )->get_href();
	return $link;
}
add_filter( 'the_permalink_rss', 'halacoupon_change_coupon_feed_link', 55 );

function halacoupon_optimize_wp_cleanup() {
    // Remove WordPress version number
    remove_action('wp_head', 'wp_generator');
    
    // Remove RSD link
    remove_action('wp_head', 'rsd_link');
    
    // Remove Windows Live Writer manifest link
    remove_action('wp_head', 'wlwmanifest_link');
    
    // Remove shortlink
    remove_action('wp_head', 'wp_shortlink_wp_head', 10, 0);
    
    // Remove oEmbed discovery links
    remove_action('wp_head', 'wp_oembed_add_discovery_links');
    
    // Remove oEmbed-specific JavaScript from the front-end
    remove_action('wp_head', 'wp_oembed_add_host_js');
    
    // Remove adjacent post links for better SEO control
    remove_action('wp_head', 'adjacent_posts_rel_link_wp_head', 10, 0);
    
    // Remove Emoji scripts and styles
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');

    // Remove feed links
    remove_action('wp_head', 'feed_links_extra', 3); // Category feeds
    remove_action('wp_head', 'feed_links', 2);       // General feeds

    // Remove comments feed links
    remove_action('wp_head', 'wp_resource_hints', 2);

    // Clean up REST API links (keeps functionality but hides the link tag)
    remove_action('wp_head', 'rest_output_link_wp_head', 10);
    remove_action('wp_head', 'wp_rest_api', 10);
}

add_action('init', 'halacoupon_optimize_wp_cleanup', 55);

/**
 * Query coupon feed
 *
 * @param $query
 * @see WP_Query
 * @see query_posts()
 */
function halacoupon_coupons_feed( $wp_query ) {
	if ( is_feed() ) {
		if (
			get_query_var( 'home_feed' ) == 'coupon'
			|| isset( $wp_query->query['coupon_store'] ) // Store feed link
			|| ( isset( $wp_query->query['taxonomy'] ) && $wp_query->query['taxonomy'] == 'coupon_store' ) // Store feed link
			|| isset( $wp_query->query['coupon_category'] ) // Category feed link
			|| ( isset( $wp_query->query['taxonomy'] ) && $wp_query->query['taxonomy'] == 'coupon_category' ) // Category feed link
		) {
			$wp_query->set( 'post_type', 'coupon' );
		}
	}
}
add_action( 'pre_get_posts', 'halacoupon_coupons_feed' );

/**
 * Change Blog posts feed link
 *
 * @see get_feed_link
 */
function halacoupon_change_feed_link( $link, $feed ) {
	if ( is_home() ) {
		global $wp_rewrite;
		if ( false === strpos( $link, 'comments' ) ) {
			$permalink = $wp_rewrite->get_feed_permastruct();
			if ( '' != $permalink ) {
				if ( get_default_feed() == $feed ) {
					$feed = '';
				}
				if ( ! $feed ) {
					$feed = '/' . $feed;
				}
				$link = trailingslashit( home_url( 'blog' . $feed ) );
			}
		}
	}
	return $link;
}

add_filter( 'feed_link', 'halacoupon_change_feed_link', 35, 2 );

/**
 * Add /blog/ prefix to single blog post permalinks
 *
 * This function modifies the permalink structure for single blog posts only,
 * adding a /blog/ prefix while maintaining all other post types unchanged.
 *
 * @param string $permalink The original permalink
 * @param WP_Post $post The post object
 * @param bool $leavename Whether to keep the post name or replace with %postname%
 * @return string Modified permalink with /blog/ prefix for posts
 */
function halacoupon_add_blog_prefix_to_posts( $permalink, $post, $leavename = false ) {
	// Only modify permalinks for 'post' post type (blog posts)
	if ( 'post' !== get_post_type( $post ) ) {
		return $permalink;
	}

	// Only modify if using permalinks (not default ?p=123 structure)
	if ( ! get_option( 'permalink_structure' ) ) {
		return $permalink;
	}

	global $wp_rewrite;

	// Get the post name for the URL
	$post_name = $leavename ? '%postname%' : $post->post_name;

	// Build the new permalink with /blog/ prefix
	if ( $wp_rewrite->using_permalinks() ) {
		$permalink = trailingslashit( home_url( 'blog/' . $post_name ) );
	}

	return $permalink;
}

/**
 * Modify blog archive links to include /blog/ prefix
 *
 * @param string $link The original archive link
 * @return string Modified link with /blog/ prefix
 */
function halacoupon_modify_blog_archive_link( $link ) {
	// Only modify the main blog archive (home page when showing posts)
	if ( is_home() && ! is_front_page() ) {
		$blog_page_id = get_option( 'page_for_posts' );
		if ( $blog_page_id ) {
			// If there's a dedicated blog page, use its URL
			return get_permalink( $blog_page_id );
		} else {
			// Otherwise, use /blog/ prefix
			return trailingslashit( home_url( 'blog' ) );
		}
	}

	return $link;
}

/**
 * Modify home URL to point to /blog/ when on blog pages
 *
 * @param string $url The home URL
 * @param string $path The path being requested
 * @param string $scheme The URL scheme
 * @return string Modified URL
 */
function halacoupon_modify_home_url_for_blog( $url, $path, $scheme ) {
	// Only modify when we're on blog-related pages
	if ( is_home() || ( is_single() && 'post' === get_post_type() ) ) {
		// If path is empty or just a slash, and we're on blog pages
		if ( empty( $path ) || $path === '/' ) {
			return trailingslashit( home_url( 'blog', $scheme ) );
		}
	}

	return $url;
}

/**
 * Fix pagination links for blog posts to include /blog/ prefix
 *
 * @param string $link The pagination link
 * @return string Modified pagination link
 */
function halacoupon_fix_blog_pagination_links( $link ) {
	// Only modify pagination on blog pages
	if ( is_home() || is_single() && 'post' === get_post_type() ) {
		// Replace the home URL with blog prefix in pagination
		$home_url = trailingslashit( home_url() );
		$blog_url = trailingslashit( home_url( 'blog' ) );

		// If the link starts with home URL, replace with blog URL
		if ( strpos( $link, $home_url . 'page/' ) === 0 ) {
			$link = str_replace( $home_url . 'page/', $blog_url . 'page/', $link );
		}
	}

	return $link;
}

/**
 * Handle category and tag archive URLs for blog posts
 *
 * @param string $link The archive link
 * @param string $taxonomy The taxonomy name
 * @return string Modified link
 */
function halacoupon_modify_blog_taxonomy_links( $link, $taxonomy = '' ) {
	// Only modify standard blog taxonomies (category and post_tag)
	if ( in_array( $taxonomy, array( 'category', 'post_tag' ) ) ) {
		// Add /blog/ prefix to category and tag archives
		$home_url = trailingslashit( home_url() );

		// Extract the taxonomy part from the URL
		if ( $taxonomy === 'category' ) {
			$category_base = get_option( 'category_base' ) ?: 'category';
			if ( strpos( $link, $home_url . $category_base . '/' ) === 0 ) {
				$link = str_replace( $home_url . $category_base . '/', $home_url . 'blog/' . $category_base . '/', $link );
			}
		} elseif ( $taxonomy === 'post_tag' ) {
			$tag_base = get_option( 'tag_base' ) ?: 'tag';
			if ( strpos( $link, $home_url . $tag_base . '/' ) === 0 ) {
				$link = str_replace( $home_url . $tag_base . '/', $home_url . 'blog/' . $tag_base . '/', $link );
			}
		}
	}

	return $link;
}

// Hook the functions to WordPress filters
add_filter( 'post_link', 'halacoupon_add_blog_prefix_to_posts', 10, 3 );
add_filter( 'post_type_link', 'halacoupon_add_blog_prefix_to_posts', 10, 3 );
add_filter( 'paginate_links', 'halacoupon_fix_blog_pagination_links', 10, 1 );
add_filter( 'home_url', 'halacoupon_modify_home_url_for_blog', 10, 3 );
add_filter( 'term_link', 'halacoupon_modify_blog_taxonomy_links', 10, 2 );

/**
 * Handle redirects from old blog post URLs to new /blog/ prefixed URLs
 *
 * This function provides backward compatibility by redirecting old blog post URLs
 * to the new /blog/ prefixed structure with a 301 redirect for SEO.
 */
function halacoupon_redirect_old_blog_urls() {
	// Only run on frontend
	if ( is_admin() ) {
		return;
	}

	global $wp_query;

	// Check if this is a 404 and might be an old blog post URL
	if ( is_404() ) {
		$request_uri = $_SERVER['REQUEST_URI'];
		$parsed_url = parse_url( $request_uri );
		$path = trim( $parsed_url['path'], '/' );

		// Skip if already has /blog/ prefix or is empty
		if ( empty( $path ) || strpos( $path, 'blog/' ) === 0 ) {
			return;
		}

		// Skip if it's a known non-blog URL pattern
		$skip_patterns = array(
			'discount-codes/',
			'out/',
			'go-store/',
			'wp-admin/',
			'wp-content/',
			'wp-includes/',
			'feed',
			'sitemap'
		);

		foreach ( $skip_patterns as $pattern ) {
			if ( strpos( $path, $pattern ) === 0 ) {
				return;
			}
		}

		// Check if this could be a blog post slug
		$potential_slug = explode( '/', $path )[0];

		// Look for a post with this slug
		$post = get_page_by_path( $potential_slug, OBJECT, 'post' );

		if ( $post && $post->post_status === 'publish' ) {
			// Redirect to the new /blog/ prefixed URL
			$new_url = trailingslashit( home_url( 'blog/' . $post->post_name ) );

			// Preserve query parameters if any
			if ( ! empty( $parsed_url['query'] ) ) {
				$new_url .= '?' . $parsed_url['query'];
			}

			wp_redirect( $new_url, 301 );
			exit;
		}
	}
}

/**
 * Flush rewrite rules when blog permalink structure is activated
 *
 * This ensures that the new rewrite rules are properly registered.
 */
function halacoupon_flush_blog_rewrite_rules() {
	// Check if we need to flush rules (only run once)
	$flushed = get_option( 'halacoupon_blog_rules_flushed', false );

	if ( ! $flushed ) {
		flush_rewrite_rules();
		update_option( 'halacoupon_blog_rules_flushed', true );
	}
}

/**
 * Reset the flush flag when theme is switched or updated
 */
function halacoupon_reset_blog_rewrite_flush() {
	delete_option( 'halacoupon_blog_rules_flushed' );
}

/**
 * Admin notice about new blog URL structure
 */
function halacoupon_blog_url_admin_notice() {
	// Only show to administrators
	if ( ! current_user_can( 'manage_options' ) ) {
		return;
	}

	// Check if notice was dismissed
	$dismissed = get_option( 'halacoupon_blog_url_notice_dismissed', false );
	if ( $dismissed ) {
		return;
	}

	// Only show on relevant admin pages
	$screen = get_current_screen();
	if ( ! $screen || ! in_array( $screen->id, array( 'themes', 'edit-post', 'post', 'options-permalink' ) ) ) {
		return;
	}

	?>
	<div class="notice notice-info is-dismissible" id="halacoupon-blog-url-notice">
		<h3><?php esc_html_e( '🔗 Blog URL Structure Updated', 'halacoupon' ); ?></h3>
		<p>
			<strong><?php esc_html_e( 'HalaCoupon Blog URLs Now Include /blog/ Prefix', 'halacoupon' ); ?></strong><br>
			<?php esc_html_e( 'All blog posts now use the /blog/ prefix for better organization and SEO.', 'halacoupon' ); ?>
		</p>
		<ul style="margin-left: 20px;">
			<li><?php esc_html_e( '✅ Single posts: yoursite.com/blog/post-name/', 'halacoupon' ); ?></li>
			<li><?php esc_html_e( '✅ Categories: yoursite.com/blog/category/category-name/', 'halacoupon' ); ?></li>
			<li><?php esc_html_e( '✅ Tags: yoursite.com/blog/tag/tag-name/', 'halacoupon' ); ?></li>
			<li><?php esc_html_e( '✅ Archives: yoursite.com/blog/2024/01/', 'halacoupon' ); ?></li>
			<li><?php esc_html_e( '✅ Authors: <AUTHORS>
		</ul>
		<p>
			<strong><?php esc_html_e( 'Automatic Features:', 'halacoupon' ); ?></strong><br>
			<?php esc_html_e( '• Old URLs automatically redirect to new structure (301 redirects)', 'halacoupon' ); ?><br>
			<?php esc_html_e( '• SEO-friendly with proper canonical URLs', 'halacoupon' ); ?><br>
			<?php esc_html_e( '• Backward compatibility maintained', 'halacoupon' ); ?>
		</p>
		<p>
			<a href="<?php echo esc_url( admin_url( 'options-permalink.php' ) ); ?>" class="button button-primary">
				<?php esc_html_e( 'Check Permalink Settings', 'halacoupon' ); ?>
			</a>
			<button type="button" class="button" id="flush-blog-rewrites">
				<?php esc_html_e( 'Refresh URL Rules', 'halacoupon' ); ?>
			</button>
			<button type="button" class="button-link" id="dismiss-blog-notice" style="margin-left: 10px;">
				<?php esc_html_e( 'Dismiss this notice', 'halacoupon' ); ?>
			</button>
		</p>
	</div>

	<script>
	jQuery(document).ready(function($) {
		// Handle dismiss button
		$('#dismiss-blog-notice').on('click', function() {
			$.post(ajaxurl, {
				action: 'halacoupon_dismiss_blog_notice',
				nonce: '<?php echo wp_create_nonce( 'halacoupon_dismiss_blog_notice' ); ?>'
			});
			$('#halacoupon-blog-url-notice').fadeOut();
		});

		// Handle flush rewrites button
		$('#flush-blog-rewrites').on('click', function() {
			var $button = $(this);
			$button.prop('disabled', true).text('<?php esc_html_e( 'Refreshing...', 'halacoupon' ); ?>');

			$.post(ajaxurl, {
				action: 'halacoupon_flush_blog_rewrites',
				nonce: '<?php echo wp_create_nonce( 'halacoupon_flush_blog_rewrites' ); ?>'
			}, function(response) {
				if (response.success) {
					$button.text('<?php esc_html_e( 'Refreshed!', 'halacoupon' ); ?>').removeClass('button').addClass('button-primary');
					setTimeout(function() {
						$button.prop('disabled', false).text('<?php esc_html_e( 'Refresh URL Rules', 'halacoupon' ); ?>').removeClass('button-primary').addClass('button');
					}, 2000);
				} else {
					$button.prop('disabled', false).text('<?php esc_html_e( 'Error - Try Again', 'halacoupon' ); ?>');
				}
			});
		});
	});
	</script>
	<?php
}

/**
 * AJAX handler to dismiss the blog URL notice
 */
function halacoupon_ajax_dismiss_blog_notice() {
	check_ajax_referer( 'halacoupon_dismiss_blog_notice', 'nonce' );

	if ( ! current_user_can( 'manage_options' ) ) {
		wp_die( -1 );
	}

	update_option( 'halacoupon_blog_url_notice_dismissed', true );
	wp_die( 1 );
}

/**
 * AJAX handler to flush blog rewrite rules
 */
function halacoupon_ajax_flush_blog_rewrites() {
	check_ajax_referer( 'halacoupon_flush_blog_rewrites', 'nonce' );

	if ( ! current_user_can( 'manage_options' ) ) {
		wp_die( -1 );
	}

	// Force flush rewrite rules
	delete_option( 'halacoupon_blog_rules_flushed' );
	flush_rewrite_rules();
	update_option( 'halacoupon_blog_rules_flushed', true );

	wp_send_json_success( array( 'message' => __( 'Blog URL rules refreshed successfully!', 'halacoupon' ) ) );
}

/**
 * Debug function to test blog URL structure (for development)
 *
 * Add ?debug_blog_urls=1 to any admin page to see URL examples
 */
function halacoupon_debug_blog_urls() {
	if ( ! current_user_can( 'manage_options' ) || ! isset( $_GET['debug_blog_urls'] ) ) {
		return;
	}

	echo '<div style="background: #fff; border: 1px solid #ccc; padding: 20px; margin: 20px; border-radius: 5px;">';
	echo '<h3>🔍 HalaCoupon Blog URL Structure Debug</h3>';

	// Get a sample post
	$sample_post = get_posts( array( 'numberposts' => 1, 'post_type' => 'post' ) );

	if ( $sample_post ) {
		$post = $sample_post[0];
		echo '<h4>Sample Post URLs:</h4>';
		echo '<ul>';
		echo '<li><strong>New URL:</strong> ' . get_permalink( $post->ID ) . '</li>';
		echo '<li><strong>Post Name:</strong> ' . $post->post_name . '</li>';
		echo '<li><strong>Post ID:</strong> ' . $post->ID . '</li>';
		echo '</ul>';
	}

	// Show rewrite rules
	global $wp_rewrite;
	echo '<h4>Blog-Related Rewrite Rules:</h4>';
	echo '<ul>';
	$rules = get_option( 'rewrite_rules' );
	foreach ( $rules as $pattern => $rewrite ) {
		if ( strpos( $pattern, 'blog' ) !== false ) {
			echo '<li><code>' . esc_html( $pattern ) . '</code> → <code>' . esc_html( $rewrite ) . '</code></li>';
		}
	}
	echo '</ul>';

	echo '<h4>URL Examples:</h4>';
	echo '<ul>';
	echo '<li>Blog Home: <code>' . home_url( 'blog' ) . '</code></li>';
	echo '<li>Blog Category: <code>' . home_url( 'blog/category/sample-category' ) . '</code></li>';
	echo '<li>Blog Tag: <code>' . home_url( 'blog/tag/sample-tag' ) . '</code></li>';
	echo '<li>Blog Archive: <code>' . home_url( 'blog/2024/01' ) . '</code></li>';
	echo '<li>Blog Author: <code>' . home_url( 'blog/author/admin' ) . '</code></li>';
	echo '</ul>';

	echo '<p><em>Add this to any admin URL: <code>?debug_blog_urls=1</code></em></p>';
	echo '</div>';
}

// Hook debug function
add_action( 'admin_footer', 'halacoupon_debug_blog_urls' );

// Hook the redirect function
add_action( 'template_redirect', 'halacoupon_redirect_old_blog_urls', 1 );

// Hook flush functions
add_action( 'init', 'halacoupon_flush_blog_rewrite_rules', 20 );
add_action( 'switch_theme', 'halacoupon_reset_blog_rewrite_flush' );
add_action( 'after_switch_theme', 'halacoupon_reset_blog_rewrite_flush' );

// Hook admin notice and AJAX handlers
add_action( 'admin_notices', 'halacoupon_blog_url_admin_notice' );
add_action( 'wp_ajax_halacoupon_dismiss_blog_notice', 'halacoupon_ajax_dismiss_blog_notice' );
add_action( 'wp_ajax_halacoupon_flush_blog_rewrites', 'halacoupon_ajax_flush_blog_rewrites' );



