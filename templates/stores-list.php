<?php
/**
 * Template Name: Stores List - Enhanced v4.0
 *
 * Completely refined stores listing page with:
 * - Featured stores slider
 * - 5-column responsive grid layout
 * - Enhanced caching system
 * - Consistent store card design
 * - Performance optimizations
 *
 * @package HalaCoupon
 * @version 4.0.0 - Complete Refinement
 */

// Security check
if (!defined('ABSPATH')) {
    exit;
}

// Include store grid functions
require_once get_template_directory() . '/inc/app/stores/store-grid.php';

get_header();
the_post();

// Get current language direction for RTL/LTR support
$is_rtl = is_rtl();
$text_direction = $is_rtl ? 'rtl' : 'ltr';

// Enhanced caching system with versioning
$cache_version = '4.0';
$cache_key = 'halacoupon_stores_list_data_v' . $cache_version;
$featured_cache_key = 'halacoupon_featured_stores_v' . $cache_version;
$cached_data = get_transient($cache_key);
$cached_featured = get_transient($featured_cache_key);

// Initialize variables
$stores_data = [];
$stores_by_letter = [];
$stores_by_number = [];
$total_stores = 0;
$total_coupons = 0;
$featured_stores = [];

?>

<div id="content-wrap" class="page-stores-list bg-background overflow-hidden" dir="<?php echo esc_attr($text_direction); ?>">

	<div id="primary" class="content-area">
		<main id="main" class="site-main" role="main">
			<?php
                // Enhanced error handling and validation with caching
                if ( taxonomy_exists( 'coupon_store' ) ) {

                    if (false === $cached_data) {
                        // Optimized query arguments for performance
                        $args = array(
                            'orderby'                => 'name',
                            'order'                  => 'ASC',
                            'hide_empty'             => false,
                            'fields'                 => 'all',
                            'update_term_meta_cache' => true,
                            'cache_domain'           => 'halacoupon_stores'
                        );

                        $stores = get_terms( 'coupon_store', $args );

                        // Enhanced error handling
                        if ( is_wp_error( $stores ) ) {
                            $stores = array();
                        }
                    } else {
                        // Use cached data
                        $stores = $cached_data['stores'];
                        $total_stores = $cached_data['total_stores'];
                        $total_coupons = $cached_data['total_coupons'];
                    }

                    // Only process data if not cached
                    if (false === $cached_data) {
                        // Count total stores and coupons for statistics
                        foreach ($stores as $store) {
                            $total_stores++;
                            if (function_exists('halacoupon_get_coupon_counts_by_type')) {
                                $coupon_counts = halacoupon_get_coupon_counts_by_type($store->term_id, 'coupon_store');
                                $total_coupons += ($coupon_counts['code'] ?? 0) + ($coupon_counts['sale'] ?? 0);
                            }
                        }

                        // Cache the processed data
                        $cache_data = array(
                            'stores' => $stores,
                            'total_stores' => $total_stores,
                            'total_coupons' => $total_coupons
                        );
                        // Cache for 6 hours for better performance with large store numbers
                        set_transient($cache_key, $cache_data, 6 * HOUR_IN_SECONDS);
                    }
                ?>
			<section class="relative py-16 sm:py-20 lg:py-24 overflow-hidden bg-gradient-to-br from-gray-900 via-black to-gray-800">
				<!-- Enhanced floating elements with better positioning -->
				<div class="absolute hidden lg:block inset-0 pointer-events-none z-0">
					<div class="absolute top-20 left-20 w-48 h-48 bg-gradient-secondary rounded-full filter blur-3xl opacity-20 animate-float"></div>
					<div class="absolute bottom-20 right-20 w-40 h-40 bg-gradient-primary rounded-full filter blur-2xl opacity-15 animate-float-slow"></div>
					<div class="absolute top-1/3 right-1/4 w-32 h-32 bg-gradient-accent rounded-full filter blur-xl opacity-10 animate-float-fast"></div>
					<!-- Additional glass blur elements -->
					<div class="absolute inset-0 bg-gradient-to-br from-black/50 via-transparent to-black/30 backdrop-blur-sm"></div>
				</div>

				<div class="relative max-w-5xl mx-auto px-6 sm:px-8 z-10">


					<h1 class="text-center text-5xl sm:text-6xl lg:text-7xl font-black font-rubik mb-8 leading-tight drop-shadow-2xl">
						<span class="bg-gradient-to-r from-white via-yellow-200 to-white bg-clip-text text-transparent">
							<?php echo esc_html(get_the_title()); ?>
						</span>
					</h1>



					<div class="flex justify-center">
						<span class="inline-flex items-center gap-3 px-6 py-3 rounded-full bg-black/60 backdrop-blur-xl text-white font-medium text-sm border-2 border-white/20 shadow-soft hover:bg-black/70 hover:border-white/30 transition-all duration-300">
							<div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
							<svg class="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
							</svg>
							<?php esc_html_e('Last Updated:', 'halacoupon'); ?> <?php echo get_current_month_info('current_day_full'); ?>
						</span>
					</div>
				</div>
			</section>


		<?php
		$page_content = get_the_content();
		if (!empty($page_content)) :
		?>
			<section class="pt-16 bg-gradient-to-br from-white via-background/30 to-white relative">
				<!-- Background Pattern -->
				<div class="absolute inset-0 opacity-5">
					<div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(235,191,67,0.3) 1px, transparent 0); background-size: 20px 20px;"></div>
				</div>

				<div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 z-10">
					<div class="bg-white/80 entry-content backdrop-blur-sm rounded-2xl p-8 lg:p-12 shadow-soft border border-white/50">
						<div class="prose prose-lg max-w-none prose-headings:scroll-mt-28">
							<?php echo wp_kses_post($page_content); ?>
						</div>
					</div>
				</div>
			</section>
		<?php endif; ?>

			<section class="py-16 bg-gradient-to-br from-background via-white to-background/50 relative">
				<!-- Background Pattern -->
				<div class="absolute inset-0 opacity-5">
					<div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(235,191,67,0.3) 1px, transparent 0); background-size: 20px 20px;"></div>
				</div>

				<div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">


				<!-- All Stores Grid Section -->
				<div class="store-listing-container">
					<div class="store-listing">
						<!-- Single Grid for All Stores -->
						<div class="store-section">
							<!-- Enhanced Store Cards Grid - 5 per row, Responsive, RTL/LTR -->
							<div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3 sm:gap-4 lg:gap-6">
								<?php
								// Display all stores using the existing store card function
								if (isset($stores) && is_array($stores)) {
									foreach ($stores as $store) {
										if (!is_object($store)) continue;

										// Get store data using the existing function
										$store_data = halacoupon_get_store_data($store);

										// Render store card using the existing function
										halacoupon_render_store_card($store_data, true);
									}
								}
								?>
							</div>
						</div>
					</div>
				</div>
			</section>
		</div>
		<?php } else { ?>
		<!-- Enhanced No Stores Found Section -->
		<section class="py-16">
			<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div class="bg-white/80 backdrop-blur-xl rounded-3xl border border-white/20 shadow-float p-12 text-center">
					<div class="max-w-md mx-auto">
						<!-- Animated Icon -->
						<div class="relative mb-8">
							<div class="w-24 h-24 mx-auto bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center animate-pulse">
								<svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
								</svg>
							</div>
							<!-- Floating elements -->
							<div class="absolute top-0 left-0 w-4 h-4 bg-primary/20 rounded-full animate-float"></div>
							<div class="absolute top-4 right-2 w-3 h-3 bg-secondary/20 rounded-full animate-float" style="animation-delay: 1s;"></div>
							<div class="absolute bottom-2 left-4 w-2 h-2 bg-accent/20 rounded-full animate-float" style="animation-delay: 2s;"></div>
						</div>

						<h3 class="text-2xl font-bold text-text mb-4"><?php esc_html_e( 'No stores found', 'halacoupon' ); ?></h3>
						<p class="text-gray-600 mb-8 leading-relaxed"><?php esc_html_e( 'We couldn\'t find any stores at the moment. Our team is constantly adding new stores and deals. Please check back later or contact us if you\'re looking for a specific store.', 'halacoupon' ); ?></p>

						<!-- Action buttons -->
						<div class="flex flex-col sm:flex-row gap-4 justify-center">
							<a href="<?php echo esc_url(home_url('/')); ?>"
							   class="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-primary to-secondary text-white rounded-xl font-medium hover:shadow-glow transition-all duration-300">
								<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
								</svg>
								<?php esc_html_e('Go to Homepage', 'halacoupon'); ?>
							</a>

							<?php if (function_exists('halacoupon_get_option') && halacoupon_get_option('contact_form_email')) : ?>
							<a href="<?php echo esc_url(get_permalink(get_page_by_path('contact'))); ?>"
							   class="inline-flex items-center gap-2 px-6 py-3 bg-white/80 backdrop-blur-sm text-text border border-gray-200 rounded-xl font-medium hover:bg-white hover:shadow-glow transition-all duration-300">
								<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
								</svg>
								<?php esc_html_e('Contact Us', 'halacoupon'); ?>
							</a>
							<?php endif; ?>
						</div>
					</div>
				</div>
			</div>
		</section>
		<?php } ?>

		<!-- Custom Content Section -->
		<?php
		$custom_content = get_post_meta(get_the_ID(), '_halacoupon_stores_list_custom_content', true);
		if (!empty($custom_content)) :
		?>
			<section class="py-16 bg-gradient-to-br from-white via-background/30 to-white relative">
				<!-- Background Pattern -->
				<div class="absolute inset-0 opacity-5">
					<div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(235,191,67,0.3) 1px, transparent 0); background-size: 20px 20px;"></div>
				</div>

				<div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 z-10">
					<div class="bg-white/80 entry-content backdrop-blur-sm rounded-2xl p-8 lg:p-12 shadow-soft border border-white/50">
						<div class="prose prose-lg max-w-none prose-headings:scroll-mt-28">
							<?php echo wp_kses_post($custom_content); ?>
						</div>
					</div>
				</div>
			</section>
		<?php endif; ?>

	</main><!-- #main -->
</div><!-- #primary -->

<?php

        wp_reset_postdata();

        ?>

</div> <!-- /#content-wrap -->









<?php get_footer(); ?>