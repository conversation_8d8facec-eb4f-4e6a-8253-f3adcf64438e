<?php
// /**
// * *********************************************
// * *                                           *
// * *                                           *
// * *     ▄████████    ▄██████▄     ▄████████   *
// * *    ███    ███   ███    ███   ███    ███   *
// * *    ███    ███   ███    █▀    ███    █▀    *
// * *    ███    ███  ▄███          ███          *
// * *  ▀███████████ ▀▀███ ████▄  ▀███████████   *
// * *    ███    ███   ███    ███          ███   *
// * *    ███    ███   ███    ███    ▄█    ███   *
// * *    ███    █▀    ████████▀   ▄████████▀    *
// * *                                           *
// * *    Abdullah G ********** +201001248698    *
// * *                                           *
// * *********************************************
// */


$theme_data   = wp_get_theme();
if ( $theme_data->exists() ) {
	define( 'ST_THEME_NAME', $theme_data->get( 'Name' ) );
	define( 'ST_THEME_VERSION', $theme_data->get( 'Version' ) );
	
}

if (!defined('halacoupon_THEME_URI')) {
    define('halacoupon_THEME_URI', get_template_directory_uri() . '/');
}

if (!defined('halacoupon_ASSETS_PATH')) {
    define('halacoupon_ASSETS_PATH', halacoupon_THEME_URI . 'assets/');
}

if (!defined('halacoupon_CSS_PATH')) {
    define('halacoupon_CSS_PATH', halacoupon_ASSETS_PATH . 'css/');
}

if (!defined('halacoupon_JS_PATH')) {
    define('halacoupon_JS_PATH', halacoupon_ASSETS_PATH . 'js/');
}

if (!defined('halacoupon_IMAGES_PATH')) {
    define('halacoupon_IMAGES_PATH', halacoupon_ASSETS_PATH . 'images/');
}





if ( ! isset( $content_width ) ) {
	$content_width = 1280; /* pixels */
}

if ( ! function_exists( 'halacoupon_theme_setup' ) ) :

	function halacoupon_theme_setup() {

		// Force load our theme's translation file with higher priority
		$locale = get_locale();
		if ($locale === 'ar' || isset($_GET['force_arabic']) || isset($_GET['lang'])) {
			$mo_file = get_template_directory() . '/languages/ar.mo';
			if (file_exists($mo_file)) {
				// Unload any existing textdomain first
				unload_textdomain('halacoupon');
				// Load our specific translation file
				load_textdomain('halacoupon', $mo_file);
			}
		} else {
			load_theme_textdomain( 'halacoupon', get_template_directory() . '/languages' );
		}
		add_theme_support( 'automatic-feed-links' );
		add_filter( 'widget_text', 'do_shortcode' );
		add_theme_support( 'title-tag' );
		add_theme_support( 'post-thumbnails' );
		add_image_size( 'halacoupon_small_thumb', 200, 115, false );
		add_image_size( 'halacoupon_medium-thumb', 480, 480, false );
		add_image_size( 'halacoupon_blog_medium', 620, 300, true );
		add_theme_support(
			'html5',
			array(
				'search-form',
				'comment-form',
				'comment-list',
				'gallery',
				'caption',
			)
		);
		register_nav_menus(
			array(
				'primary' => esc_html__( 'Primary', 'halacoupon' ),
				'footer_menu_1' => __( 'Footer Menu 1', 'halacoupon' ),
				'footer_menu_2' => __( 'Footer Menu 2', 'halacoupon' ),
				'footer_menu_3' => __( 'Footer Menu 3', 'halacoupon' ),
			)
		);

	}
endif;
add_action( 'after_setup_theme', 'halacoupon_theme_setup' );



require get_template_directory() . '/inc/ags-int.php';

/**
 * Add font preload links for performance optimization (Rubik Font - Local)
 * Preloads critical Rubik font files for optimal performance
 */
function halacoupon_add_font_preloads() {
    $theme_uri = get_template_directory_uri();

    // Preload critical Rubik font weights for optimal performance
    // Regular (400) - Most commonly used
    echo '<link rel="preload" href="' . $theme_uri . '/assets/css/fonts/Rubik-Regular.ttf" as="font" type="font/ttf" crossorigin>';

    // Bold (700) - Commonly used for headings
    echo '<link rel="preload" href="' . $theme_uri . '/assets/css/fonts/Rubik-Bold.ttf" as="font" type="font/ttf" crossorigin>';

    // SemiBold (600) - Used for emphasis
    echo '<link rel="preload" href="' . $theme_uri . '/assets/css/fonts/Rubik-SemiBold.ttf" as="font" type="font/ttf" crossorigin>';

    // Medium (500) - Used for UI elements
    echo '<link rel="preload" href="' . $theme_uri . '/assets/css/fonts/Rubik-Medium.ttf" as="font" type="font/ttf" crossorigin>';
}
add_action('wp_head', 'halacoupon_add_font_preloads', 1);

/**
 * Add critical image preloads for performance optimization
 *
 * This function preloads essential images to improve page loading performance:
 * - Website logo (all pages) - for immediate brand visibility
 * - Featured images (single posts, reviews) - for faster hero section rendering
 * - Store images (store taxonomy pages) - for better store page performance
 *
 * Preloading reduces Largest Contentful Paint (LCP) and improves Core Web Vitals.
 * Uses fetchpriority="high" for maximum loading priority.
 */
function halacoupon_add_critical_image_preloads() {
    // Always preload the website logo first (highest priority)
    $site_logo = halacoupon_get_option('site_logo', false, 'url');
    if (!empty($site_logo)) {
        echo '<link rel="preload" href="' . esc_url($site_logo) . '" as="image" fetchpriority="high">';
    }

    // Preload featured images on specific pages
    if (is_singular('post') || is_singular('review') || is_tax('coupon_store')) {

        if (is_singular('post') || is_singular('review')) {
            // For single posts and reviews - preload featured image
            $featured_image_url = get_the_post_thumbnail_url(get_the_ID(), 'full');
            if ($featured_image_url) {
                echo '<link rel="preload" href="' . esc_url($featured_image_url) . '" as="image" fetchpriority="high">';
            }

        } elseif (is_tax('coupon_store')) {
            // For store taxonomy pages - preload store image
            $term = get_queried_object();
            if ($term && isset($term->term_id)) {

                // Try to get store image using the store system
                if (function_exists('halacoupon_setup_store') && function_exists('halacoupon_store')) {
                    halacoupon_setup_store($term);
                    $store = halacoupon_store();
                    if ($store && method_exists($store, 'get_thumbnail')) {
                        $store_image_url = $store->get_thumbnail('full', true); // url_only = true
                        if ($store_image_url) {
                            echo '<link rel="preload" href="' . esc_url($store_image_url) . '" as="image" fetchpriority="high">';
                        }
                    }
                } else {
                    // Fallback: try to get store image from term meta
                    $store_image = get_term_meta($term->term_id, '_ags_store_image', true);
                    if ($store_image) {
                        echo '<link rel="preload" href="' . esc_url($store_image) . '" as="image" fetchpriority="high">';
                    }
                }
            }
        }
    }
}
add_action('wp_head', 'halacoupon_add_critical_image_preloads', 2);

/**
 * Admin notice for new preloading system (temporary)
 * Shows information about the new critical image preloading feature
 */
function halacoupon_preloading_admin_notice() {
    if (current_user_can('manage_options')) {
        $screen = get_current_screen();
        if ($screen && $screen->id === 'themes') {
            echo '<div class="notice notice-success is-dismissible">';
            echo '<p><strong>🚀 HalaCoupon Performance Enhancement:</strong> Critical image preloading is now active! ';
            echo 'Your website logo and featured images will preload for faster page loading. ';
            echo '<a href="' . admin_url('themes.php?page=halacoupon-performance') . '">Learn more</a></p>';
            echo '</div>';
        }
    }
}
add_action('admin_notices', 'halacoupon_preloading_admin_notice');

/**
 * Localize modal strings for JavaScript translation support
 */
function halacoupon_localize_modal_strings() {
    // Only localize if modals script is enqueued
    if (wp_script_is('halacoupon-global-coupon-modal', 'enqueued')) {
        wp_localize_script('halacoupon-global-coupon-modal', 'halacoupon_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('halacoupon_ajax_nonce'),
            'strings' => array(
                'coupon_code'         => __('Coupon Code', 'halacoupon'),
                'copy_short'          => __('COPY', 'halacoupon'),
                'copy_paste_checkout' => __('Copy & paste at checkout', 'halacoupon'),
                'copy_code_button'    => __('Copy & Visit Store', 'halacoupon'),
                'get_deal_now'        => __('Get Deal Now', 'halacoupon'),
                'get_deal_button'     => __('Get Deal', 'halacoupon'),
                'no_code_needed'      => __('No code needed', 'halacoupon'),
                'share_link_copied'   => __('Share link copied!', 'halacoupon'),
                'copied_success'      => __('Copied!', 'halacoupon'),
                'copy_failed'         => __('Copy failed', 'halacoupon'),
                'copy_share_link'     => __('Copy share link', 'halacoupon'),
                'code_copied_redirect' => __('Code copied! Redirecting...', 'halacoupon'),
                'getting_deal_redirect' => __('Getting deal... Redirecting...', 'halacoupon'),
                'loading_deal'        => __('Loading Amazing Deal...', 'halacoupon'),
                'please_wait'         => __('Please wait while we prepare your coupon', 'halacoupon'),
                'copy_code'           => __('Copy', 'halacoupon'),
                'code_copied'         => __('Code Copied!', 'halacoupon'),
                'get_deal'            => __('Get Deal', 'halacoupon'),
                'share_coupon'        => __('Share Coupon', 'halacoupon'),
                'close'               => __('Close', 'halacoupon'),
                'error_occurred'      => __('An error occurred. Please try again.', 'halacoupon'),
                'vote_success'        => __('Vote recorded successfully!', 'halacoupon'),
                'vote_error'          => __('Failed to record vote. Please try again.', 'halacoupon'),
                'already_voted'       => __('You have already voted for this coupon.', 'halacoupon'),
                'special_offer'       => __('Special Offer', 'halacoupon'),
                'store'               => __('Store', 'halacoupon'),
                'expires'             => __('Expires:', 'halacoupon'),
                'success_rate'        => __('Success Rate', 'halacoupon'),
                'used_today'          => __('Used Today', 'halacoupon'),
                'coupon_details'      => __('Coupon Details', 'halacoupon'),
                'terms_conditions'    => __('Terms & Conditions', 'halacoupon'),
                'share_this_coupon'   => __('Share this coupon', 'halacoupon'),

                // Modal specific strings
                'store_opened_message' => __('The %s website has been opened in a new tab / window. Simply copy and paste the code %s and enter it at the checkout.', 'halacoupon'),
                'store_opened_no_code_message' => __('The %s website has been opened in a new tab / window. No code needed - discount applied automatically at checkout.', 'halacoupon'),
                'no_code_needed_message' => __('No code needed - discount applied automatically', 'halacoupon'),
                'copy_visit_store'    => __('Copy & Visit Store', 'halacoupon'),
                'copy_button'         => __('Copy', 'halacoupon'),
                'helpful_question'    => __('Helpful?', 'halacoupon'),
                'you_voted'           => __('You have already voted for this coupon', 'halacoupon'),
                'copy_share_link'     => __('Copy share link', 'halacoupon'),
                'store_fallback'      => __('store', 'halacoupon'),

                // Additional modal strings
                'special_offer'       => __('Special Offer', 'halacoupon'),
                'store'               => __('Store', 'halacoupon'),
                'code_copied_redirect' => __('Code copied! Redirecting to store...', 'halacoupon'),
                'getting_deal_redirect' => __('Getting deal! Redirecting to store...', 'halacoupon'),
                'vote_label_already'  => __('You have already voted for this coupon', 'halacoupon'),
                'vote_label'          => __('Vote if this coupon worked for you', 'halacoupon'),
                'like_label'          => __('Helpful?', 'halacoupon'),
                'no_code_needed'      => __('No code needed', 'halacoupon'),
                'get_deal_now'        => __('Get Deal Now', 'halacoupon'),
                'get_deal_button'     => __('Get Deal Now', 'halacoupon'),
                'exclusive'           => __('Exclusive', 'halacoupon'),
                'verified'            => __('Verified', 'halacoupon'),
                'expires'             => __('Expires:', 'halacoupon'),
                'copy_failed'         => __('Copy failed. Please copy manually.', 'halacoupon'),
            )
        ));
    }
}
add_action('wp_enqueue_scripts', 'halacoupon_localize_modal_strings', 35);

/**
 * Enhanced Cache Management for Stores List
 * Automatically clears cache when stores or coupons are updated
 */
function halacoupon_clear_stores_cache() {
    $cache_version = '4.0';
    $cache_key = 'halacoupon_stores_list_data_v' . $cache_version;
    $featured_cache_key = 'halacoupon_featured_stores_v' . $cache_version;

    delete_transient($cache_key);
    delete_transient($featured_cache_key);

    // Clear any related object cache
    wp_cache_delete('halacoupon_stores', 'terms');
    wp_cache_delete('halacoupon_featured_stores', 'terms');
}

// Hook into store/coupon updates to clear cache
add_action('created_coupon_store', 'halacoupon_clear_stores_cache');
add_action('edited_coupon_store', 'halacoupon_clear_stores_cache');
add_action('deleted_coupon_store', 'halacoupon_clear_stores_cache');
add_action('save_post_coupon', 'halacoupon_clear_stores_cache');
add_action('delete_post', function($post_id) {
    if (get_post_type($post_id) === 'coupon') {
        halacoupon_clear_stores_cache();
    }
});

/**
 * Manual cache clearing function for admin use
 */
function halacoupon_manual_clear_stores_cache() {
    if (current_user_can('manage_options')) {
        halacoupon_clear_stores_cache();
        wp_cache_flush(); // Clear all object cache
        return true;
    }
    return false;
}

/**
 * Add language-specific body classes for font switching
 */
function halacoupon_add_language_body_class($classes) {
    if (is_rtl()) {
        $classes[] = 'lang-ar';
        $classes[] = 'font-rubik';
    } else {
        $classes[] = 'lang-en';
        $classes[] = 'font-rubik';
    }
    return $classes;
}
add_filter('body_class', 'halacoupon_add_language_body_class');

/**
 * Enqueue RTL enhancement styles for modern page templates
 */
function halacoupon_enqueue_rtl_enhancements() {
    // Only enqueue RTL styles if RTL is active
    if (is_rtl()) {
        wp_enqueue_style(
            'halacoupon-rtl-enhancements',
            get_template_directory_uri() . '/assets/css/rtl-enhancements.css',
            array(), // Dependencies
            ST_THEME_VERSION,
            'all'
        );
    }
}
add_action('wp_enqueue_scripts', 'halacoupon_enqueue_rtl_enhancements', 15);

/**
 * Enqueue scripts and styles.
 */


/**
 * Helper lib
 */
require_once get_template_directory() . '/inc/core/helper.php';

/**
 * Theme Options
 */
if ( class_exists( 'ReduxFramework' ) ) {
	require_once get_template_directory() . '/inc/config/option-config.php';
}


// Retrieve theme option values
if ( ! function_exists( 'halacoupon_get_option' ) ) {
    /**
     * Retrieves an option value from the global settings.
     *
     * @param string $id The ID of the option.
     * @param mixed $fallback The fallback value if the option is not set.
     * @param string|bool $key (Optional) A specific key to retrieve from the option array.
     * @return mixed The option value or the fallback if not found.
     */
    function halacoupon_get_option( $id, $fallback = '', $key = false ) {
        global $ag_option;
        
        // Get the options array if not already fetched.
        if ( ! isset( $ag_option ) ) {
            $ag_option = get_option( 'ag_halacoupon', array() ); // Ensure $ag_option is an array by default.
        }

        // If the options array is not an array, return the fallback.
        if ( ! is_array( $ag_option ) ) {
            return $fallback;
        }

        // Check if the specified option ID exists.
        if ( isset( $ag_option[ $id ] ) && $ag_option[ $id ] !== '' ) {
            $output = $ag_option[ $id ];

            // If a key is specified and exists within the option array, return it.
            if ( $key && is_array( $output ) && isset( $output[ $key ] ) ) {
                return $output[ $key ];
            }

            return $output; // Return the main option value.
        }

        // Return the fallback if the option is not found or empty.
        return $fallback;
    }
}

/**
 * Support coupon type
 *
 * @return array
 */
function halacoupon_get_coupon_types( $plural = false ){


    if ( $plural ) {
        $types = array(
            'code'       => esc_html__( 'Codes', 'halacoupon' ),
            'sale'       => esc_html__( 'Sales', 'coupolala' ),
        );

    } else {
        $types = array(
            'code'       => esc_html__( 'Code', 'halacoupon' ),
            'sale'       => esc_html__( 'Sale', 'halacoupon' ),
        );

    }
    return apply_filters( 'coupolala_get_coupon_types', $types, $plural );
}




/**
 * Recommend plugins via TGM activation class
 */
require_once get_template_directory() . '/inc/tgmpa/tgmpa-config.php';


/**
 * Post type
 */
require_once get_template_directory() . '/inc/post-type.php';


/**
 * Coupon functions.
 */
require_once get_template_directory() . '/inc/core/coupon.php';

/**
 * Coupon functions.
 */
require_once get_template_directory() . '/inc/core/store.php';


/**
 * Coupon functions.
 */
require_once get_template_directory() . '/inc/core/sharing.php';

/**
 * Search functions.
 */
require_once get_template_directory() . '/inc/core/search.php';


/**
 * Ajax handle
 */
require_once get_template_directory() . '/inc/core/ajax.php';



/**
 * Custom template tags for this theme.
 */
require_once get_template_directory() . '/inc/template-tags.php';
require_once get_template_directory() . '/inc/newsletter.php';
require_once get_template_directory() . '/inc/rating.php';
require_once get_template_directory() . '/inc/likes.php';
require_once get_template_directory() . '/inc/menu-icon.php';
require_once get_template_directory() . '/inc/app/reviews/reviews-functions.php';
require_once get_template_directory() . '/inc/app/placeholders/dynamic-placeholders.php';
require_once get_template_directory() . '/inc/app/placeholders/seo-examples.php';




if ( is_multisite() ) {
    $file = get_template_directory() . '/inc/network-config.php';
    if ( file_exists( $file ) ) {
        require_once $file;
    }
}










/**
 * Custom functions that act independently of the theme templates.
 */
require_once get_template_directory() . '/inc/extras.php';

/**
 * Load custom metaboxes config.
 */
require_once get_template_directory() . '/inc/config/metabox-config.php';





require_once get_template_directory() . '/out.php';



function halacoupon_excerpt_length($length) {
    return 20;
}
add_filter('excerpt_length', 'halacoupon_excerpt_length', 999);
  






/**
 * Enqueue Global Coupon Modal CSS
 */
function halacoupon_enqueue_global_coupon_modal_css() {
    wp_enqueue_style(
        'halacoupon-global-coupon-modal',
        get_template_directory_uri() . '/assets/css/components/global-coupon-modal.css',
        array(),
        ST_THEME_VERSION
    );
}
add_action('wp_enqueue_scripts', 'halacoupon_enqueue_global_coupon_modal_css', 25);

/**
 * Enqueue Global Coupon Modal JS
 */
function halacoupon_enqueue_modal_js() {
        wp_enqueue_script(
            'halacoupon-global-coupon-modal',
            get_template_directory_uri() . '/assets/js/modals.js',
            array('jquery', 'halacoupon_global'),
            ST_THEME_VERSION,
            true
        );

}
add_action('wp_enqueue_scripts', 'halacoupon_enqueue_modal_js', 30);

/**
 * Enqueue Dynamic Placeholders JS
 */
function halacoupon_enqueue_placeholders_js() {
    wp_enqueue_script(
        'halacoupon-dynamic-placeholders',
        get_template_directory_uri() . '/assets/js/dynamic-placeholders.js',
        array('jquery'),
        ST_THEME_VERSION,
        true
    );

    // Localize essential placeholder data only
    wp_localize_script('halacoupon-dynamic-placeholders', 'halacoupon_placeholders', array(
        // Essential Site Information
        'site_title' => get_bloginfo('name'),
        'site_name' => get_bloginfo('name'),
        'site_url' => get_site_url(),
        'home_url' => get_home_url(),

        // Multisite Support
        'network_name' => is_multisite() ? get_network()->site_name : get_bloginfo('name'),
        'network_url' => is_multisite() ? network_home_url() : get_home_url(),
        'blog_id' => get_current_blog_id(),

        // Geographic & Localization
        'country' => apply_filters('halacoupon_placeholder_country', ''),
        'currency' => function_exists('get_woocommerce_currency') ? get_woocommerce_currency() : 'USD',
        'currency_symbol' => function_exists('get_woocommerce_currency_symbol') ? get_woocommerce_currency_symbol() : '$',

        // Essential Theme Data
        'total_coupons' => wp_count_posts('coupon')->publish ?? 0,
        'total_stores' => wp_count_terms(array('taxonomy' => 'coupon_store', 'hide_empty' => false)) ?: 0,
    ));
}
add_action('wp_enqueue_scripts', 'halacoupon_enqueue_placeholders_js', 25);

// Localize modal strings after scripts are enqueued
add_action('wp_enqueue_scripts', 'halacoupon_localize_modal_strings', 35);








