<?php
// /**
// * *********************************************
// * *                                           *
// * *                                           *
// * *     ▄████████    ▄██████▄     ▄████████   *
// * *    ███    ███   ███    ███   ███    ███   *
// * *    ███    ███   ███    █▀    ███    █▀    *
// * *    ███    ███  ▄███          ███          *
// * *  ▀███████████ ▀▀███ ████▄  ▀███████████   *
// * *    ███    ███   ███    ███          ███   *
// * *    ███    ███   ███    ███    ▄█    ███   *
// * *    ███    █▀    ████████▀   ▄████████▀    *
// * *                                           *
// * *    Abdullah G ********** +201001248698    *
// * *                                           *
// * *********************************************
// */




get_header();

$term = get_queried_object();
halacoupon_setup_store( $term );
$current_link = get_permalink( $term );
$cate_id = get_queried_object_id();
$icon = get_term_meta( $cate_id, '_ags_icon', true );



?>


<div id="content-wrap" class="store-right-sidbar">

	<div id="primary" class="content-area single-store-content">
		<main id="main" class="site-main coupon-store-main" role="main">

			<!-- Category Header -->
			<header class="border-b border-gray-200" style="background: linear-gradient(135deg, #1f2937 0%, #111827 50%, #1f2937 100%);">
				<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
					<div class="flex items-center space-x-6">
						<!-- Category Icon -->
						<div class="flex-shrink-0">
							<div class="w-20 h-20 bg-white rounded-xl flex items-center justify-center">
								<?php if ( $icon ) : ?>
								<i class="category <?php echo esc_attr( $icon ); ?> text-3xl text-primary-600"></i>
								<?php else : ?>
								<svg class="w-10 h-10 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
								</svg>
								<?php endif; ?>
							</div>
						</div>

						<!-- Category Details -->
						<div class="flex-1 min-w-0">
							<h1 class="text-3xl font-bold text-gray-900 mb-3">
								<?php echo halacoupon_store()->get_single_store_name(); ?>
							</h1>

							<!-- Category Rating -->
							<?php if(halacoupon_display_star_rating($cate_id) ) : ?>
							<div class="mb-4">
								<div class="flex items-center space-x-2">
									<?php halacoupon_display_star_rating($cate_id); ?>
								</div>
							</div>
							<?php endif; ?>

							<!-- Category Description -->
							<?php
							$description = get_the_archive_description();
							if ( $description ) : ?>
							<div class="content-typography prose prose-gray max-w-none">
								<div class="category-description text-gray-600 leading-relaxed">
									<?php echo halacoupon_toggle_content_more( $description ); ?>
								</div>
							</div>
							<?php endif; ?>
						</div>
					</div>
				</div>
			</header>



			<!-- Category Content Area -->
			<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
				<div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
					<!-- Main Content -->
					<section id="category-content" class="lg:col-span-3">
						<!-- CSS-Based Filter Buttons -->
						<div class="mb-6">
							<div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
								<div class="flex flex-wrap gap-2" id="coupon-type-filters">
									<button type="button"
										class="filter-btn active px-4 py-2 bg-primary-600 text-white rounded-lg font-medium transition-colors duration-200 hover:bg-primary-700"
										data-filter="all">
										<span class="flex items-center">
											<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
											</svg>
											<?php esc_html_e('All', 'halacoupon'); ?>
											<span class="ml-1 px-2 py-0.5 bg-white/20 rounded-full text-xs" id="count-all">0</span>
										</span>
									</button>
									<button type="button"
										class="filter-btn px-4 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium transition-colors duration-200 hover:bg-gray-200"
										data-filter="code">
										<span class="flex items-center">
											<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
											</svg>
											<?php esc_html_e('Codes', 'halacoupon'); ?>
											<span class="ml-1 px-2 py-0.5 bg-gray-200 rounded-full text-xs" id="count-code">0</span>
										</span>
									</button>
									<button type="button"
										class="filter-btn px-4 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium transition-colors duration-200 hover:bg-gray-200"
										data-filter="sale">
										<span class="flex items-center">
											<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
											</svg>
											<?php esc_html_e('Sales', 'halacoupon'); ?>
											<span class="ml-1 px-2 py-0.5 bg-gray-200 rounded-full text-xs" id="count-sale">0</span>
										</span>
									</button>
								</div>
							</div>
						</div>

						<!-- Coupon Listings -->
						<div id="coupon-listings-store" class="space-y-6">
							<?php
							$cate_id = get_queried_object_id();
							$args = halacoupon_get_coupon_query_args($cate_id, 'all', array(), array(), 'coupon_category');
							$coupons = new WP_Query($args);

							if ($coupons->have_posts()) {
								while ($coupons->have_posts()) {
									$coupons->the_post();
									halacoupon_setup_coupon( );
									get_template_part('loop/loop-coupon');
								}
							} else {
								echo '<div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">';
								echo '<div class="max-w-md mx-auto">';
								echo '<svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
								echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>';
								echo '</svg>';
								echo '<h3 class="text-lg font-semibold text-gray-900 mb-2">' . esc_html__('No coupons found', 'halacoupon') . '</h3>';
								echo '<p class="text-gray-600">' . esc_html__('There are currently no active coupons in this category. Please check back later.', 'halacoupon') . '</p>';
								echo '</div>';
								echo '</div>';
							}
							wp_reset_postdata();
							?>
						</div>
					</section>

					<!-- Sidebar -->
					<aside class="lg:col-span-1">
						<div class="sticky top-24 space-y-6">
							<?php get_sidebar( 'category' );?>
						</div>
					</aside>
				</div>
			</div>


		</main><!-- #main -->
	</div><!-- #primary -->


</div> <!-- /#content-wrap -->

<?php get_footer(); ?>