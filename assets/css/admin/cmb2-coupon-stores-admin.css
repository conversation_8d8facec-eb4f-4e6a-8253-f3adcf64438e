/**
 * CMB2 Coupon Stores Taxonomy Admin Interface Enhancement
 * Enhanced styling for coupon stores taxonomy admin interface only
 * Supports both LTR and RTL languages
 */

/* ==========================================================================
   Base Styles for Coupon Store Taxonomy Pages
   ========================================================================== */

/* Target only coupon_store taxonomy pages */
body.taxonomy-coupon_store {
    background-color: #f6f7f7;
}

/* Enhanced CMB2 Metabox Container */
.taxonomy-coupon_store .cmb2-metabox {
    background: #ffffff;
	padding: 10px;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    margin-bottom: 24px;
    overflow: hidden;
    transition: box-shadow 0.2s ease;
}


.taxonomy-coupon_store  .inside.cmb-td.cmb-nested.cmb-field-list {
	padding: 10px!important;
}

.taxonomy-coupon_store .cmb2-metabox:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.cmb-row.cmb-type-wysiwyg.cmb2-id--ags-faq-group-0-answer.cmb-repeat-group-field,
.cmb-row.cmb-remove-field-row{
		padding: 10px!important;
}

/* Metabox Headers */
.taxonomy-coupon_store .cmb2-metabox h2.hndle {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    padding: 16px 20px;
    border-bottom: none;
    position: relative;
}

.taxonomy-coupon_store .cmb2-metabox h2.hndle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #EBBF43, #FFBC02);
}

/* ==========================================================================
   Form Fields Enhancement
   ========================================================================== */

/* CMB2 Field Rows */
.taxonomy-coupon_store .cmb-row {
    padding: 20px;
    border-bottom: 1px solid #f0f2f4;
    transition: background-color 0.2s ease;
}

.taxonomy-coupon_store .cmb-row:last-child {
    border-bottom: none;
}

.taxonomy-coupon_store .cmb-row:hover {
    background-color: #fafbfc;
}

/* Field Labels */
.taxonomy-coupon_store .cmb-th label {
    color: #2c3e50;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
    line-height: 1.4;
}

/* Field Descriptions */
.taxonomy-coupon_store .cmb-th .cmb2-metabox-description {
    color: #64748b;
    font-size: 13px;
    font-style: normal;
    margin-top: 4px;
    line-height: 1.5;
}

/* Text Inputs */
.taxonomy-coupon_store .cmb2-text-input,
.taxonomy-coupon_store .cmb2-text-medium,
.taxonomy-coupon_store .cmb2-text-url {
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    padding: 12px 16px;
    font-size: 14px;
    line-height: 1.4;
    transition: all 0.2s ease;
    background-color: #ffffff;
    width: 100%;
    max-width: 400px;
}

.taxonomy-coupon_store .cmb2-text-input:focus,
.taxonomy-coupon_store .cmb2-text-medium:focus,
.taxonomy-coupon_store .cmb2-text-url:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

/* Textareas */
.taxonomy-coupon_store .cmb2-textarea {
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    padding: 12px 16px;
    font-size: 14px;
    line-height: 1.6;
    transition: all 0.2s ease;
    background-color: #ffffff;
    resize: vertical;
    min-height: 120px;
}

.taxonomy-coupon_store .cmb2-textarea:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

/* Select Dropdowns */
.taxonomy-coupon_store .cmb2-select {
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    padding: 12px 16px;
    font-size: 14px;
    background-color: #ffffff;
    transition: all 0.2s ease;
    max-width: 300px;
}

.taxonomy-coupon_store .cmb2-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

/* Checkboxes */
.taxonomy-coupon_store .cmb2-checkbox {
    width: 18px;
    height: 18px;
    border: 2px solid #e2e8f0;
    border-radius: 4px;
    margin-right: 8px;
    transition: all 0.2s ease;
}

.taxonomy-coupon_store .cmb2-checkbox:checked {
    background-color: #667eea;
    border-color: #667eea;
}

/* File Upload Fields */
.taxonomy-coupon_store .cmb2-upload-file {
    border: 2px dashed #cbd5e0;
    border-radius: 8px;
    padding: 24px;
    text-align: center;
    background-color: #f8fafc;
    transition: all 0.2s ease;
}

.taxonomy-coupon_store .cmb2-upload-file:hover {
    border-color: #667eea;
    background-color: #f0f4ff;
}

.taxonomy-coupon_store .cmb2-upload-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    border: none;
    border-radius: 6px;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.taxonomy-coupon_store .cmb2-upload-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* ==========================================================================
   CMB2 Group/Repeater Fields (FAQ Section)
   ========================================================================== */

/* Group Field Container */
.taxonomy-coupon_store .cmb-group-wrap {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 16px;
    margin-top: 12px;
}

/* Individual Group Items */
.taxonomy-coupon_store .cmb-group {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    margin-bottom: 16px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.taxonomy-coupon_store .cmb-group:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* Group Title Bar */
.taxonomy-coupon_store .cmb-group-title {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    padding: 12px 16px;
    border-bottom: 1px solid #e2e8f0;
    font-weight: 600;
    color: #334155;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Group Controls */
.taxonomy-coupon_store .cmb-group-title .cmb-group-controls {
    display: flex;
    gap: 8px;
}

/* Drag Handle */
.taxonomy-coupon_store .cmb-group-title .cmb-handle {
    color: #64748b;
    cursor: move;
    font-size: 16px;
    padding: 4px;
}

.taxonomy-coupon_store .cmb-group-title .cmb-handle:hover {
    color: #334155;
}

/* Remove Button */
.taxonomy-coupon_store .cmb-remove-group-row {
    background: #ef4444;
    color: #ffffff;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.taxonomy-coupon_store .cmb-remove-group-row:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

/* Add Group Button */
.taxonomy-coupon_store .cmb-add-group-row {
    background: linear-gradient(135deg, #10b981 0%, #**********%);
    color: #ffffff;
    border: none;
    border-radius: 6px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 16px;
}

.taxonomy-coupon_store .cmb-add-group-row:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* Group Field Content */
.taxonomy-coupon_store .cmb-group .cmb-row {
    padding: 16px;
    border-bottom: 1px solid #f1f5f9;
}

.taxonomy-coupon_store .cmb-group .cmb-row:last-child {
    border-bottom: none;
}

/* ==========================================================================
   WYSIWYG Editor Enhancement
   ========================================================================== */

.taxonomy-coupon_store .wp-editor-wrap {
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.taxonomy-coupon_store .wp-editor-wrap:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* ==========================================================================
   Select2 Enhancement (for store selection)
   ========================================================================== */

.taxonomy-coupon_store .select2-container {
    max-width: 400px;
}

.taxonomy-coupon_store .select2-selection {
    border: 2px solid #e2e8f0 !important;
    border-radius: 6px !important;
    padding: 8px 12px !important;
    min-height: 44px !important;
}

.taxonomy-coupon_store .select2-selection:focus,
.taxonomy-coupon_store .select2-container--open .select2-selection {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .taxonomy-coupon_store .cmb-row {
        padding: 16px;
    }
    
    .taxonomy-coupon_store .cmb2-text-input,
    .taxonomy-coupon_store .cmb2-text-medium,
    .taxonomy-coupon_store .cmb2-text-url,
    .taxonomy-coupon_store .cmb2-select {
        max-width: 100%;
    }
    
    .taxonomy-coupon_store .cmb-group-title {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }
}

/* ==========================================================================
   Success/Error States
   ========================================================================== */

.taxonomy-coupon_store .cmb2-field-error {
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

.taxonomy-coupon_store .cmb2-field-success {
    border-color: #10b981 !important;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
}


.cmb-type-group .cmb-th+.cmb-td, .cmb2-postbox .cmb-th+.cmb-td {
    width: 100%!important;
}


.regular-text,
.cmb2-wrap input.cmb2-text-medium {
    width: 100%!important;
}

#edittag .cmb-td {
	    margin-left: 0px!important;
}