# HalaCoupon Font System - Local Implementation

## Overview
The HalaCoupon theme uses **Rubik** as the universal font family to support both LTR (Left-to-Right) and RTL (Right-to-Left) languages:

- **Rubik**: Modern sans-serif font with excellent readability for both LTR and RTL languages
- **Universal Design**: Single font family that works beautifully across all languages
- **Local Implementation**: All fonts hosted locally for optimal performance and GDPR compliance
- **Complete Coverage**: Full weight and style coverage (300-900, normal + italic)

## Font Loading - Local Implementation
All fonts are loaded locally for optimal performance and to avoid external dependencies:

### Rubik Font Family
- **Family**: Rubik
- **Weights**: 300 (Light), 400 (Regular), 500 (Medium), 600 (SemiBold), 700 (Bold), 800 (ExtraBold), 900 (Black)
- **Styles**: Normal + Italic for each weight
- **Format**: TTF (TrueType) - excellent browser support and compatibility
- **Unicode Range**: Latin + Extended Latin characters
- **Usage**: Universal (LTR and RTL languages)
- **Features**: Slightly rounded corners, excellent readability, modern design
- **Performance**: Local hosting eliminates external requests and improves loading speed

## CSS Classes

### Font Family Classes
- `.font-rubik` - Apply Rubik font (primary)
- `.font-sans` - Apply Rubik font (Tailwind default)
- `.font-mono` - Apply monospace font stack

### Language-Specific Classes
- `.lang-ar`, `[lang="ar"]`, `[dir="rtl"]` - Use Rubik (RTL optimized)
- `.lang-en`, `[lang="en"]`, `[dir="ltr"]` - Use Rubik (LTR optimized)

## PHP Integration

### Body Classes
The theme automatically adds language-specific body classes:
- `lang-ar font-rubik` for RTL
- `lang-en font-rubik` for LTR

### Font Preloading
Critical font files are preloaded for optimal performance:
- Rubik Variable font (primary)
- Rubik 400 & 700 static fonts (fallback)

## Performance Optimizations

1. **Variable Fonts**: Primary use of Rubik Variable for optimal file size
2. **Font Display**: `swap` for optimal loading experience
3. **Preloading**: Critical fonts preloaded in `<head>`
4. **Static Fallbacks**: Individual weight files for browser compatibility
5. **Unicode Ranges**: Optimized character sets for performance
6. **Local Hosting**: All fonts hosted locally for better performance and privacy

## Usage Examples

```css
/* Apply Rubik font */
.my-element {
  @apply font-rubik; /* or font-sans */
}

/* Monospace for code */
.code-element {
  @apply font-mono;
}

/* Language-specific styling */
[dir="rtl"] .my-element {
  /* RTL-specific styles - Rubik handles RTL well */
}

[dir="ltr"] .my-element {
  /* LTR-specific styles */
}
```

## Font Weights Available
- **300**: Light
- **400**: Regular (default)
- **500**: Medium
- **600**: SemiBold
- **700**: Bold
- **800**: ExtraBold
- **900**: Black

## Files Structure
```
assets/css/fonts/
├── fonts.css                    # Complete @font-face declarations
├── Rubik-Light.ttf             # 300 Normal
├── Rubik-LightItalic.ttf       # 300 Italic
├── Rubik-Regular.ttf           # 400 Normal (default)
├── Rubik-Italic.ttf            # 400 Italic
├── Rubik-Medium.ttf            # 500 Normal
├── Rubik-MediumItalic.ttf      # 500 Italic
├── Rubik-SemiBold.ttf          # 600 Normal
├── Rubik-SemiBoldItalic.ttf    # 600 Italic
├── Rubik-Bold.ttf              # 700 Normal
├── Rubik-BoldItalic.ttf        # 700 Italic
├── Rubik-ExtraBold.ttf         # 800 Normal
├── Rubik-ExtraBoldItalic.ttf   # 800 Italic
├── Rubik-Black.ttf             # 900 Normal
├── Rubik-BlackItalic.ttf       # 900 Italic
└── README.md                   # This documentation
```

## Local Implementation Benefits
- ✅ **No External Requests**: Eliminates dependency on Google Fonts CDN
- ✅ **GDPR Compliance**: No data sent to third-party services
- ✅ **Offline Functionality**: Fonts work without internet connection
- ✅ **Consistent Performance**: No network latency or CDN downtime issues
- ✅ **Better Caching**: Fonts cached with your site's assets
- ✅ **Full Control**: Complete control over font loading and optimization
