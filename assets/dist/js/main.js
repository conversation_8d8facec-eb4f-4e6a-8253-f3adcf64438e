/*! For license information please see main.js.LICENSE.txt */
"use strict";(self.webpackChunkhalacoupon=self.webpackChunkhalacoupon||[]).push([[829],{178:()=>{},589:()=>{},709:()=>{},759:(e,t,n)=>{var i=n(692),o=n.n(i);window.HalaCoupon=window.HalaCoupon||{config:{version:"1.0.0",debug:!1,ajaxUrl:"",nonce:"",homeUrl:"",storeSlug:"discount-codes",enableSingle:!1,autoOpenModal:!1,voteExpires:7,userLoggedIn:!1,headerSticky:!1,salePreviewTab:!0,codePreviewTab:!0,couponClickAction:"prev"},strings:{loading:"Loading...",readLess:"Read Less",error:"Error loading content. Please try again.",copied:"Copied",copy:"Copy",tryDifferent:"Try a different search term",store:"Store",coupon:"Coupon",clickToView:"Click to view"},modules:{},initialized:!1,init:function(){var e=this;this.initialized||("undefined"!=typeof ST&&(this.config=Object.assign(this.config,ST)),this.initEventListeners(),this.initAjaxSetup(),this.initAccessibility(),this.initPerformanceOptimizations(),Object.keys(this.modules).forEach(function(t){"function"==typeof e.modules[t].init&&e.modules[t].init()}),this.initialized=!0,this.trigger("theme:initialized"))},trigger:function(e){var t=new CustomEvent(e,{detail:arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},bubbles:!0,cancelable:!0});document.dispatchEvent(t)},on:function(e,t){document.addEventListener(e,t)},off:function(e,t){document.removeEventListener(e,t)},initEventListeners:function(){var e,t=this;window.addEventListener("resize",function(){clearTimeout(e),e=setTimeout(function(){t.trigger("theme:resize")},250)});var n=!1;window.addEventListener("scroll",function(){n||(window.requestAnimationFrame(function(){t.trigger("theme:scroll",{scrollTop:window.pageYOffset,scrollLeft:window.pageXOffset}),n=!1}),n=!0)}),document.addEventListener("visibilitychange",function(){t.trigger("theme:visibility",{hidden:document.hidden})}),window.addEventListener("beforeunload",function(){t.trigger("theme:beforeunload")})},initAjaxSetup:function(){"undefined"!=typeof jQuery&&jQuery.ajaxSetup({beforeSend:function(e,t){t.data&&-1===t.data.indexOf("_wpnonce")&&(t.data+="&_wpnonce="+HalaCoupon.config.nonce)}})},initAccessibility:function(){document.querySelectorAll(".skip-link").forEach(function(e){e.addEventListener("click",function(e){e.preventDefault();var t=document.querySelector(this.getAttribute("href"));t&&(t.focus(),t.scrollIntoView({behavior:"smooth"}))})}),document.addEventListener("keydown",function(e){"Escape"===e.key&&HalaCoupon.trigger("theme:escape"),"Tab"===e.key&&HalaCoupon.trigger("theme:tab",{shiftKey:e.shiftKey})})},initPerformanceOptimizations:function(){if("IntersectionObserver"in window){var e=new IntersectionObserver(function(t){t.forEach(function(t){if(t.isIntersecting){var n=t.target;n.dataset.src&&(n.src=n.dataset.src,n.classList.remove("lazy"),e.unobserve(n))}})},{rootMargin:"50px 0px",threshold:.01});document.querySelectorAll("img[data-src]").forEach(function(t){e.observe(t)})}this.preloadCriticalResources()},preloadCriticalResources:function(){[{href:this.config.homeUrl+"/wp-content/themes/halacoupon/assets/dist/css/main.css",as:"style"},{href:this.config.homeUrl+"/wp-content/themes/halacoupon/assets/dist/js/vendor.js",as:"script"}].forEach(function(e){var t=document.createElement("link");t.rel="preload",t.href=e.href,t.as=e.as,document.head.appendChild(t)})},debounce:function(e,t,n){var i;return function(){var o=this,r=arguments,a=n&&!i;clearTimeout(i),i=setTimeout(function(){i=null,n||e.apply(o,r)},t),a&&e.apply(o,r)}},throttle:function(e,t){var n;return function(){var i=arguments;n||(e.apply(this,i),n=!0,setTimeout(function(){return n=!1},t))}},isInViewport:function(e){var t=e.getBoundingClientRect();return t.top>=0&&t.left>=0&&t.bottom<=(window.innerHeight||document.documentElement.clientHeight)&&t.right<=(window.innerWidth||document.documentElement.clientWidth)},getCookie:function(e){var t=("; "+document.cookie).split("; "+e+"=");return 2===t.length?t.pop().split(";").shift():null},setCookie:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:7,i=new Date;i.setTime(i.getTime()+24*n*60*60*1e3),document.cookie=e+"="+t+";expires="+i.toUTCString()+";path=/"},formatNumber:function(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")},sanitizeHTML:function(e){var t=document.createElement("div");return t.textContent=e,t.innerHTML}},"loading"===document.readyState?document.addEventListener("DOMContentLoaded",function(){window.HalaCoupon.init()}):window.HalaCoupon.init();window.HalaCoupon;window.$=window.jQuery=o(),o()(document).ready(function(){window.HalaCoupon&&window.HalaCoupon.init(),o()(".skip-link").on("click",function(e){e.preventDefault();var t=o()(this).attr("href");o()(t).focus()}),o()(".dropdown-toggle").on("keydown",function(e){"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),o()(this).click())}),o()("[data-toggle]").each(function(){var e=o()(this),t=e.data("toggle"),n=o()(t);n.length&&(e.attr("aria-controls",t.replace("#","")),e.attr("aria-expanded",n.is(":visible")))}),function(){if("IntersectionObserver"in window){var e=new IntersectionObserver(function(e,t){e.forEach(function(e){if(e.isIntersecting){var n=e.target;n.src=n.dataset.src,n.classList.remove("lazy"),t.unobserve(n)}})});document.querySelectorAll("img[data-src]").forEach(function(t){e.observe(t)})}["/wp-content/themes/halacoupon/assets/dist/css/main.css","/wp-content/themes/halacoupon/assets/dist/js/vendor.js"].forEach(function(e){var t=document.createElement("link");t.rel="preload",t.href=e,t.as=e.endsWith(".css")?"style":"script",document.head.appendChild(t)})}()}),window.addEventListener("error",function(e){"undefined"!=typeof gtag&&gtag("event","exception",{description:e.message,fatal:!1})}),window.addEventListener("unhandledrejection",function(e){"undefined"!=typeof gtag&&gtag("event","exception",{description:"Unhandled Promise Rejection: "+e.reason,fatal:!1})})}},e=>{var t=t=>e(e.s=t);e.O(0,[552,247,364,660],()=>(t(759),t(589),t(709),t(178)));e.O()}]);