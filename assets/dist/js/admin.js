/*! For license information please see admin.js.LICENSE.txt */
(self.webpackChunkhalacoupon=self.webpackChunkhalacoupon||[]).push([[567],{899:()=>{jQuery(document).ready(function(e){!function(){var e,n=jQuery;n(document).on("click",".menu-icon-upload",function(t){t.preventDefault();var a=n(this),o=(a.data("field-id"),a.siblings(".menu-icon-preview")),i=a.siblings('input[type="hidden"]');e||(e=wp.media({title:"Choose Menu Icon",button:{text:"Use this image"},multiple:!1,library:{type:"image"}})).on("select",function(){var n=e.state().get("selection").first().toJSON();o.css("background-image","url(".concat(n.url,")")),o.show(),i.val(n.id),a.siblings(".menu-icon-remove").show(),a.text("Change Icon")}),e.open()}),n(document).on("click",".menu-icon-remove",function(e){e.preventDefault();var t=n(this),a=t.siblings(".menu-icon-upload"),o=t.siblings(".menu-icon-preview"),i=t.siblings('input[type="hidden"]');o.css("background-image",""),o.hide(),i.val(""),t.hide(),a.text("Upload Icon")})}(),function(){var e=jQuery;e(document).on("change",'select[name="coupon_type"]',function(){var n=e(this).val(),t=e(".coupon-code-field"),a=e(".coupon-url-field");"code"===n?(t.show(),a.hide()):(t.hide(),a.show())}),e.fn.datepicker&&e(".date-picker").datepicker({dateFormat:"yy-mm-dd",changeMonth:!0,changeYear:!0,minDate:0});e.fn.wpColorPicker&&e(".color-picker").wpColorPicker();e(".character-counter").each(function(){var n=e(this),t=n.attr("maxlength"),a=e('<div class="char-count"></div>');function o(){var e=t-n.val().length;a.text("".concat(e," characters remaining")),e<20?a.addClass("warning"):a.removeClass("warning")}n.after(a),n.on("input",o),o()})}(),function(){var e=jQuery;setTimeout(function(){e(".notice.notice-success").fadeOut()},5e3),e(document).on("click",".notice-dismiss",function(){e(this).closest(".notice").fadeOut()}),window.HalaCouponAdmin=window.HalaCouponAdmin||{},window.HalaCouponAdmin.notify=function(n){var t=e('\n            <div class="notice notice-'.concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",' is-dismissible">\n                <p>').concat(n,'</p>\n                <button type="button" class="notice-dismiss">\n                    <span class="screen-reader-text">Dismiss this notice.</span>\n                </button>\n            </div>\n        '));e(".wrap h1").after(t),setTimeout(function(){t.fadeOut()},5e3)}}(),function(){var e=jQuery;e("form").on("submit",function(n){var t=!0,a=e(this);if(a.find(".field-error").remove(),a.find(".error").removeClass("error"),a.find("[required]").each(function(){var n=e(this);n.val().trim()||(t=!1,n.addClass("error"),n.after('<span class="field-error">This field is required.</span>'))}),a.find('input[type="email"]').each(function(){var n=e(this),a=n.val().trim();a&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a)&&(t=!1,n.addClass("error"),n.after('<span class="field-error">Please enter a valid email address.</span>'))}),a.find('input[type="url"]').each(function(){var n=e(this),a=n.val().trim();a&&!/^https?:\/\/.+/.test(a)&&(t=!1,n.addClass("error"),n.after('<span class="field-error">Please enter a valid URL starting with http:// or https://</span>'))}),!t){n.preventDefault();var o=a.find(".error").first();o.length&&e("html, body").animate({scrollTop:o.offset().top-100},500)}})}(),function(){var e=jQuery;e(document).on("submit",".ajax-form",function(n){n.preventDefault();var t=e(this),a=t.find('[type="submit"]'),o=a.text();a.prop("disabled",!0).text("Processing...");var i=new FormData(this);i.append("action",t.data("action")),i.append("nonce",t.data("nonce")),e.ajax({url:ajaxurl,type:"POST",data:i,processData:!1,contentType:!1,success:function(e){e.success?(window.HalaCouponAdmin.notify(e.data.message,"success"),t.data("reset-on-success")&&t[0].reset()):window.HalaCouponAdmin.notify(e.data.message||"An error occurred.","error")},error:function(){window.HalaCouponAdmin.notify("Network error. Please try again.","error")},complete:function(){a.prop("disabled",!1).text(o)}})}),e(document).on("click",".bulk-action-btn",function(n){n.preventDefault();var t=e(this),a=t.data("action"),o=e(".bulk-checkbox:checked");if(0!==o.length){if(confirm("Are you sure you want to ".concat(a," ").concat(o.length," item(s)?"))){var i=o.map(function(){return e(this).val()}).get();t.prop("disabled",!0).text("Processing..."),e.ajax({url:ajaxurl,type:"POST",data:{action:"halacoupon_bulk_action",bulk_action:a,ids:i,nonce:t.data("nonce")},success:function(e){e.success?(window.HalaCouponAdmin.notify(e.data.message,"success"),location.reload()):window.HalaCouponAdmin.notify(e.data.message||"An error occurred.","error")},error:function(){window.HalaCouponAdmin.notify("Network error. Please try again.","error")},complete:function(){t.prop("disabled",!1).text(t.data("original-text"))}})}}else alert("Please select at least one item.")})}()})}},e=>{var n;n=899,e(e.s=n)}]);