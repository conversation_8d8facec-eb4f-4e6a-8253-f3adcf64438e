/**
 * 🎯 OPTIMIZED MODAL SYSTEM - Clean & Accessible
 * Streamlined implementation with proper accessibility
 */

jQuery(document).ready(function($) {
    let currentModalId = null;
    let isModalAnimating = false;

    $(document).on('click', '.fresh-coupon-btn, a[data-type="sale"][data-coupon-id], a[data-coupon-id][data-type]:not(.fresh-coupon-btn)', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const $element = $(this);
        const couponId = $element.data('coupon-id');
        const type = $element.data('type');
        const code = $element.data('code');
        const affUrl = $element.data('aff-url');
        const elementType = $element.hasClass('fresh-coupon-btn') ? 'button' : 'link';

        if (couponId && affUrl) {
            handleDirectCouponAction(couponId, type, code, affUrl);
        }
    });

    initLikeButtons();

    function extractCouponData($card, $element) {
        // Use data attributes from the <article> for robust extraction
        const cardEl = $card.get(0);
        const couponData = {
            id: $element.data('coupon-id'),
            type: $element.data('type'),
            code: $element.data('code'),
            affUrl: $element.data('aff-url'),
            title: cardEl ? cardEl.getAttribute('data-coupon-title') : '',
            storeName: cardEl ? cardEl.getAttribute('data-coupon-store') : '',
            terms: cardEl ? cardEl.getAttribute('data-coupon-terms') : '',
            discount: cardEl ? cardEl.getAttribute('data-coupon-discount') : '',
            usedToday: cardEl ? cardEl.getAttribute('data-coupon-used-today') : '',
            expiry: cardEl ? cardEl.getAttribute('data-coupon-expiry') : '',
            // No code needed field for sale/deal coupons
            no_code_needed: cardEl ? cardEl.getAttribute('data-coupon-no-code') : '',
            // Like button state from DOM (if present)
            voteCount: $card.find('.like-btn').data('vote-count') || 0,
            totalVotes: $card.find('.like-btn').data('total-votes') || 0,
            hasVoted: $card.find('.like-btn').data('has-voted') || 0,
            // Fallbacks using classes if needed
            storeImg: $card.find('.coupon-store-img-wrapper img, .coupon-store-img').first().attr('src') || '',
        };



        // Badges and other fields can be extracted similarly if needed
        // Provide fallback for missing title/storeName
        if (!couponData.title) couponData.title = halacoupon_ajax.strings.special_offer || '';
        if (!couponData.storeName) couponData.storeName = halacoupon_ajax.strings.store || '';
        return couponData;
    }

    // � FORMAT STORE MESSAGE WITH TRANSLATIONS
    function formatStoreMessage(storeName, code) {
        const store = storeName || halacoupon_ajax.strings.store_fallback || 'store';

        // Check if this is a no-code case
        const isNoCode = !code ||
                        code === '' ||
                        code === null ||
                        code === undefined ||
                        code === halacoupon_ajax.strings.no_code_needed ||
                        (typeof code === 'string' && (
                            code.toLowerCase().includes('no code') ||
                            code.toLowerCase().includes('no_code') ||
                            code.toLowerCase().includes('automatic') ||
                            code.toLowerCase().includes('applied automatically')
                        ));

        if (isNoCode) {
            // Use no-code template
            const noCodeTemplate = halacoupon_ajax.strings.store_opened_no_code_message ||
                                  halacoupon_ajax.strings.no_code_needed_message ||
                                  'The %s website has been opened in a new tab / window. No code needed - discount applied automatically at checkout.';
            return noCodeTemplate.replace('%s', store);
        } else {
            // Use regular code template
            const codeTemplate = halacoupon_ajax.strings.store_opened_message ||
                               'The %s website has been opened in a new tab / window. Simply copy and paste the code %s and enter it at the checkout.';
            return codeTemplate.replace('%s', store).replace('%s', code);
        }
    }

    // �🎯 GET TOTAL USED COUNT VIA AJAX
    function getCouponTotalUsed(couponId, callback) {
        // Try to get from global coupon data first
        if (window.halacouponData && window.halacouponData[couponId]) {
            const totalUsed = window.halacouponData[couponId].totalUsed || '0';
            callback(totalUsed);
            return;
        }

        // Fallback: Use a simple method to estimate or get from DOM
        // In a real implementation, you might want to add an AJAX endpoint
        // For now, we'll use a default value
        callback('0');
    }

    // 🔗 DIRECT COUPON ACTION HANDLER (SAME AS OLD SYSTEM)
    function handleDirectCouponAction(couponId, type, code, affUrl) {
        if (!affUrl) {
            return;
        }
        trackCouponUsage(couponId, type);
        if (type === 'code' && code) {
            copyTextToClipboard(code, function() {
                showFeedback({message: halacoupon_ajax.strings.code_copied_redirect, type: 'success', context: 'direct'});
            }, function() {
                showFeedback({message: halacoupon_ajax.strings.code_copied_redirect, type: 'success', context: 'direct'});
            });
        } else if (type === 'sale') {
            showFeedback({message: halacoupon_ajax.strings.getting_deal_redirect, type: 'success', context: 'direct'});
        }
        setTimeout(() => {
            window.location.href = affUrl;
            const storePageUrl = getStorePageUrlWithHash(couponId);
            window.open(storePageUrl, '_blank');
        }, type === 'code' ? 1500 : 800);
    }

    // Unified feedback helper
    function showFeedback({message, type = 'info', context = 'modal'}) {
        if (context === 'modal') {
            showModalFeedback(message, type);
        } else if (context === 'direct') {
            // Show direct feedback as toast in top right
            let feedback = document.getElementById('direct-coupon-feedback');
            if (!feedback) {
                feedback = document.createElement('div');
                feedback.id = 'direct-coupon-feedback';
                feedback.style.cssText = `
                    position: fixed; top: 20px; right: 20px; z-index: 99999;
                    background: #3b82f6; color: white;
                    padding: 12px 24px; border-radius: 8px; font-size: 14px; font-weight: 600;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15); transform: translateX(100%);
                    transition: transform 0.3s ease-out;`;
                document.body.appendChild(feedback);
            }
            feedback.textContent = message;
            feedback.style.background = type === 'success' ? '#10b981' : '#3b82f6';
            setTimeout(() => {
                feedback.style.transform = 'translateX(0)';
            }, 10);
            setTimeout(() => {
                feedback.style.transform = 'translateX(100%)';
            }, 3000);
        }
    }

    // ♿ ACCESSIBILITY - KEYBOARD SUPPORT & FOCUS MANAGEMENT
    // Custom, lightweight focus trap implementation
    let focusTrapHandler = null;

    function trapFocus(modal) {
        const focusableSelectors = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';
        const focusableElements = Array.from(modal.querySelectorAll(focusableSelectors)).filter(el => !el.disabled && el.offsetParent !== null);
        if (focusableElements.length === 0) return;
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        focusTrapHandler = function(e) {
            if (e.key === 'Tab') {
                if (focusableElements.length === 1) {
                    e.preventDefault();
                    firstElement.focus();
                    return;
                }
                if (e.shiftKey) {
                    if (document.activeElement === firstElement) {
                        e.preventDefault();
                        lastElement.focus();
                    }
                } else {
                    if (document.activeElement === lastElement) {
                        e.preventDefault();
                        firstElement.focus();
                    }
                }
            }
        };
        modal.addEventListener('keydown', focusTrapHandler);
    }

    function releaseFocusTrap(modal) {
        if (focusTrapHandler) {
            modal.removeEventListener('keydown', focusTrapHandler);
            focusTrapHandler = null;
        }
    }

    // Replace old document-level keydown for Tab trapping
    $(document).on('keydown', function(e) {
        const modal = document.getElementById('global-coupon-modal');
        if (!modal || modal.style.display !== 'flex') return;

        if (e.key === 'Escape' || e.keyCode === 27) {
            closeSimpleModal();
        }
    });

    // 🎯 OPTIMIZED MODAL OPENING WITH ACCESSIBILITY
    window.openSimpleModal = function(couponData) {
        // Store the currently focused element to restore later
        window.modalPreviousFocus = document.activeElement;

        // Reset modal state
        const modal = document.getElementById('global-coupon-modal');
        modal.style.display = 'none';
        modal.style.opacity = '';
        modal.style.transform = '';
        isModalAnimating = false;
        currentModalId = couponData.id;

        // Set ARIA attributes for accessibility
        modal.setAttribute('aria-labelledby', 'modal-title');
        modal.setAttribute('aria-describedby', 'modal-content');
        modal.setAttribute('role', 'dialog');
        modal.setAttribute('aria-modal', 'true');

        // Set enhanced title with RTL support
        const isRTL = document.documentElement.dir === 'rtl' || document.body.classList.contains('rtl');
        const flexDirection = isRTL ? 'row-reverse' : 'row';
        const textAlign = isRTL ? 'right' : 'left';

        const titleEl = document.getElementById('modal-title');
        const contentEl = document.getElementById('modal-content');

        titleEl.innerHTML = `
            <div style="display: flex; align-items: center; gap: 12px; flex-direction: ${flexDirection}; direction: ${isRTL ? 'rtl' : 'ltr'};">
                ${couponData.storeImg ? `
                    <img src="${couponData.storeImg}" alt="${couponData.storeName}"
                         style="width: 56px; height: 56px; border-radius: 12px; object-fit: cover;
                                border: 3px solid rgba(255,255,255,0.3); box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
                ` : ''}
                <div style="flex: 1; text-align: ${textAlign};">
                    <div style="font-size: 20px; font-weight: bold; margin-bottom: 4px; color: #220e07;">
                        ${couponData.title}
                    </div>
                    <div style="font-size: 14px; opacity: 0.8; color: #220e07; margin-bottom: 8px;">
                        ${couponData.storeName}
                    </div>
                    <div style="display: flex; gap: 8px; flex-direction: ${flexDirection}; flex-wrap: wrap;">
                        ${couponData.isHot ? `
                            <span style="background: linear-gradient(135deg, #FFBC02 0%, #e6a902 100%); color: white;
                                         padding: 4px 8px; border-radius: 6px; font-size: 11px; font-weight: 600;">
                                🔥 ${isRTL ? 'ساخن' : 'HOT'}
                            </span>
                        ` : ''}
                        ${couponData.isExclusive ? `
                            <span style="background: linear-gradient(135deg, #EBBF43 0%, #d4ab3d 100%); color: white;
                                         padding: 4px 8px; border-radius: 6px; font-size: 11px; font-weight: 600;">
                                ⭐ ${isRTL ? 'حصري' : 'EXCLUSIVE'}
                            </span>
                        ` : ''}
                        ${couponData.isVerified ? `
                            <span style="background: linear-gradient(135deg, #000000 0%, #383838 100%); color: white;
                                         padding: 4px 8px; border-radius: 6px; font-size: 11px; font-weight: 600;">
                                ✅ ${isRTL ? 'موثق' : 'VERIFIED'}
                            </span>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;

        // Set content based on type
        if (couponData.type === 'sale') {
            contentEl.innerHTML = createSaleContent(couponData);
        } else {
            contentEl.innerHTML = createCodeContent(couponData);
        }

        // Show modal with accessibility
        modal.style.display = 'flex';
        modal.style.opacity = '1';
        modal.style.transform = 'scale(1)';
        document.body.style.overflow = 'hidden';

        // Focus the first interactive element in the modal
        setTimeout(() => {
            const focusable = modal.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
            if (focusable.length > 0) {
                focusable[0].focus();
            } else {
                modal.focus(); // Fallback: focus modal container
            }
            trapFocus(modal); // Activate focus trap
        }, 100);

        // Attach terms accordion toggle logic after content is set
        setTimeout(function() {
            const btn = document.getElementById('terms-toggle-' + couponData.id);
            const panel = document.getElementById('terms-panel-' + couponData.id);



            if (btn && panel) {
                // Check if event handler already attached
                if (btn.hasAttribute('data-accordion-initialized')) {
                    return;
                }

                // Mark as initialized
                btn.setAttribute('data-accordion-initialized', 'true');

                // Initialize accordion in collapsed state
                btn.setAttribute('aria-expanded', 'false');
                panel.classList.add('hidden');
                panel.style.display = 'none';
                panel.setAttribute('aria-hidden', 'true');

                btn.addEventListener('click', function(e) {
                    // Prevent event bubbling and default behavior
                    e.preventDefault();
                    e.stopPropagation();
                    e.stopImmediatePropagation();

                    const expanded = btn.getAttribute('aria-expanded') === 'true';
                    btn.setAttribute('aria-expanded', !expanded);

                    if (expanded) {
                        // Collapse
                        panel.style.display = 'none';
                        panel.classList.add('hidden');
                        panel.setAttribute('aria-hidden', 'true');
                    } else {
                        // Expand
                        panel.style.display = 'block';
                        panel.classList.remove('hidden');
                        panel.setAttribute('aria-hidden', 'false');
                    }

                    const icon = btn.querySelector('.accordion-icon');
                    if (icon) {
                        icon.style.transform = expanded ? 'rotate(0deg)' : 'rotate(180deg)';
                    }


                }, { once: false, passive: false });

                btn.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        btn.click();
                    }
                });
            }
        }, 150);

        // After modal content is injected, trigger couponModalLoaded for dynamic like button support
        document.dispatchEvent(new CustomEvent('couponModalLoaded'));
    };
    
    window.closeSimpleModal = function() {
        const modal = document.getElementById('global-coupon-modal');
        if (modal) {
            // Hide modal
            modal.style.display = 'none';
            document.body.style.overflow = '';

            // Remove ARIA attributes
            modal.removeAttribute('aria-labelledby');
            modal.removeAttribute('aria-describedby');
            modal.removeAttribute('role');
            modal.removeAttribute('aria-modal');

            // Restore focus to the element that opened the modal
            if (window.modalPreviousFocus && typeof window.modalPreviousFocus.focus === 'function') {
                try {
                    window.modalPreviousFocus.focus();
                } catch (e) {
                    document.body.focus();
                }
                window.modalPreviousFocus = null;
            } else {
                document.body.focus();
            }

            // Reset state
            currentModalId = null;
            isModalAnimating = false;

            // Clean up focus trap and any other modal-specific listeners
            releaseFocusTrap(modal);
        }
    };
    
    // 🎯 CREATE ENHANCED TERMS & CONDITIONS ACCORDION WITH TAILWIND CLASSES ONLY
    function createTermsAccordion(terms, id, isRTL, textAlign) {

        // Accessible, collapsed by default
        return `
            <div class="modal-accordion mb-4 pt-4 pb-4 px-4 bg-gray-100 rounded-xl">
                <button id="terms-toggle-${id}" class="modal-accordion-toggle flex w-full items-center justify-between gap-2 text-gray-700 font-semibold text-base cursor-pointer mb-2 bg-transparent border-0 focus:outline-none focus:ring-2 focus:ring-blue-400" aria-expanded="false" aria-controls="terms-panel-${id}">
                    <span>${halacoupon_ajax.strings.terms_conditions || 'Terms & Conditions'}</span>
                    <svg class="accordion-icon transition-transform duration-300 w-5 h-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 10.585l3.71-3.355a.75.75 0 111.02 1.1l-4.25 3.85a.75.75 0 01-1.02 0l-4.25-3.85a.75.75 0 01.02-1.06z" clip-rule="evenodd"/></svg>
                </button>
                <div id="terms-panel-${id}" class="modal-accordion-panel hidden bg-gray-50 p-5 rounded-xl text-${textAlign}">
                    <div class="m-0 text-gray-700 leading-relaxed prose prose-sm max-w-none">${terms}</div>
                </div>
            </div>
        `;
    }

    // Render like button with real state (sync with PHP logic)
    function renderLikeButton(couponData) {
        // Use the same structure and classes as in PHP for consistency
        const isLiked = couponData.hasVoted == 1 || couponData.hasVoted === '1';
        const buttonClasses = isLiked
            ? 'like-btn flex items-center gap-1 px-4 py-2 rounded-full text-sm font-bold shadow-soft transition-all duration-300 transform bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 border-green-200 cursor-not-allowed opacity-75'
            : 'like-btn flex items-center gap-1 px-4 py-2 rounded-full text-sm font-bold shadow-soft transition-all duration-300 transform hover:scale-105 border bg-gradient-to-r from-primary-100 to-accent-100 hover:bg-gradient-primary text-primary-700 hover:text-white hover:shadow-medium border-primary-200';
        const iconClasses = isLiked ? 'w-4 h-4 text-green-600' : 'w-4 h-4';
        const ariaLabel = isLiked
            ? (halacoupon_ajax.strings.vote_label_already || 'You have already voted for this coupon')
            : (halacoupon_ajax.strings.vote_label || 'Vote if this coupon worked for you');
        return `
        <div class="like-button-container flex justify-center">
            <button
                class="${buttonClasses}"
                data-coupon-id="${couponData.id}"
                data-vote-count="${couponData.voteCount}"
                data-total-votes="${couponData.totalVotes}"
                data-has-voted="${isLiked ? '1' : '0'}"
                type="button"
                ${isLiked ? 'disabled' : ''}
                aria-label="${ariaLabel}"
            >
                <svg class="${iconClasses}" fill="${isLiked ? 'currentColor' : 'none'}" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"/>
                </svg>
                <span class="font-bold vote-count">${couponData.voteCount}</span>
                ${couponData.totalVotes > couponData.voteCount ? `<span class="text-xs opacity-75 total-votes">/${couponData.totalVotes}</span>` : ''}
                <span class="ml-2 text-sm text-gray-600 font-medium">${halacoupon_ajax.strings.like_label || 'Helpful?'}</span>
            </button>
        </div>
        `;
    }

    function createSaleContent(couponData) {
        const isRTL = document.documentElement.dir === 'rtl' || document.body.classList.contains('rtl');
        const textAlign = isRTL ? 'right' : 'left';
        const flexDirection = isRTL ? 'flex-row-reverse' : 'flex-row';

        return `
            <div class="text-center p-10 sm:p-8" dir="${isRTL ? 'rtl' : 'ltr'}">
                <div class="mb-6">
                    <div class="text-5xl mb-4">🔥</div>
                    <div class="text-2xl font-bold mb-3">${couponData.discount || halacoupon_ajax.strings.special_offer}</div>
                    <div class="text-base opacity-90 mb-6">${formatStoreMessage(couponData.storeName, couponData.no_code_needed)} </div>
                    <button onclick="goToStore('${couponData.affUrl}', '${couponData.id}')"
                            aria-label="${halacoupon_ajax.strings.get_deal_now}"
                            class="bg-white flex align-center cursor-pointer justify-center gap-1 btn-outline text-black px-8 py-4 rounded-xl font-bold text-base transition-all duration-200 w-full shadow-md hover:scale-105 focus:outline-none focus:ring-2 focus:ring-yellow-400">
                        ${halacoupon_ajax.strings.get_deal_button}
					<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25"></path>
                    </svg>
                    </button>
                </div>

                ${couponData.terms ? `
                    ${createTermsAccordion(couponData.terms, couponData.id, isRTL, textAlign)}
                ` : ''}

                <div class="bg-gray-50 p-5 rounded-xl">
                    <div class="text-base  font-semibold text-gray-800 mb-3 flex flex-row justify-between items-center gap-2">
                        <span>${halacoupon_ajax.strings.share_this_coupon}</span>
						${renderLikeButton(couponData)}
                    </div>
                    <div class="flex gap-2 items-center">
                        <input type="text"
                               value="${getStorePageUrlWithHash(couponData.id)}"
                               readonly
                               id="share-url-sale-${couponData.id}"
                               class="flex-1 p-3 border border-gray-300 rounded-lg text-sm bg-white text-gray-700" />
                        <button onclick="copyShareUrl('share-url-sale-${couponData.id}')"
                                aria-label="${halacoupon_ajax.strings.copy_share_link}"
                                class="btn-outline text-gray-800 px-4 py-3 rounded-lg font-semibold whitespace-nowrap transition-colors duration-200 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-400">
                            ${halacoupon_ajax.strings.copy_short}
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
    
    function createCodeContent(couponData) {
        const isRTL = document.documentElement.dir === 'rtl' || document.body.classList.contains('rtl');
        const textAlign = isRTL ? 'right' : 'left';
        const flexDirection = isRTL ? 'flex-row-reverse' : 'flex-row';

        return `
            <div class="text-center p-10 sm:p-8" dir="${isRTL ? 'rtl' : 'ltr'}">
                <!-- Code Hero Section -->
			  <div class="text-sm text-black opacity-90 mb-6">${formatStoreMessage(couponData.storeName, couponData.code)}</div>
                <div class="mb-6 flex flex-col gap-4 items-center justify-center">
                    <div id="code-box-${couponData.id}"
                         role="button"
                         tabindex="0"
                         aria-label="${halacoupon_ajax.strings.copy_short}"
                         class="bg-gray-200 p-4 rounded-xl mb-4 font-mono text-xl font-bold tracking-widest border-2 border-dashed w-full border-white/30 relative transition-all duration-300 cursor-pointer select-all focus:outline-none focus:ring-2 focus:ring-yellow-400"
                         onclick="copyCodeToClipboard('${couponData.code}', '${couponData.id}')"
                         onkeydown="if(event.key==='Enter'||event.key===' ') copyCodeToClipboard('${couponData.code}', '${couponData.id}')">
                        ${couponData.code || halacoupon_ajax.strings.special_offer}
                        <div id="copy-indicator-${couponData.id}" class="absolute left-2 right-auto top-1/2 -translate-y-1/2 p-4 bg-secondary text-black font-rubik rounded-md text-xs font-semibold transition-all duration-300">
                            ${halacoupon_ajax.strings.copy_short}
                        </div>
                    </div>
                    <button onclick="copyCodeAndGoToStore('${couponData.code}', '${couponData.affUrl}', '${couponData.id}')"
                            aria-label="${halacoupon_ajax.strings.copy_code_button}"
                            class="px-3 py-2 text-base flex items-center gap-2 transition-all duration-200 min-w-auto cursor-pointer underline focus:outline-none focus:ring-2 focus:ring-yellow-400">
                        ${halacoupon_ajax.strings.copy_code_button}
						<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25"></path>
                    </svg>
                    </button>
                </div>
                
                ${couponData.terms ? `
                    ${createTermsAccordion(couponData.terms, couponData.id, isRTL, textAlign)}
                ` : ''}

                <div class="rounded-xl mt-6 text-${textAlign}">
                    <div class="text-base font-semibold text-gray-800 mb-3 flex flex-row justify-between items-center gap-2">
                        <span>${halacoupon_ajax.strings.share_this_coupon}</span>
						 ${renderLikeButton(couponData)}
                    </div>
                    <div class="flex gap-2 items-center flex-row">
                        <input type="text"
                               value="${getStorePageUrlWithHash(couponData.id)}"
                               readonly
                               id="share-url-code-${couponData.id}"
                               class="flex-1 p-3 border border-gray-300 rounded-lg text-sm bg-white text-gray-700" />
                        <button onclick="copyShareUrl('share-url-code-${couponData.id}')"
                                aria-label="${halacoupon_ajax.strings.copy_share_link}"
                                class="btn-outline text-garay-800 px-4 py-3 rounded-lg font-semibold whitespace-nowrap transition-colors duration-200 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-400">
                            ${halacoupon_ajax.strings.copy_short}
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
    
    // 🎯 ENHANCED UTILITY FUNCTIONS WITH AFFILIATE LOGIC
    window.goToStore = function(affUrl, couponId) {
        if (affUrl && couponId) {
            // Show feedback
            showFeedback({message: 'Processing deal... 🛒', type: 'info', context: 'modal'});

            // Track coupon usage first
            trackCouponUsage(couponId, 'sale');

            // Open affiliate URL in current page (like old logic)
            setTimeout(() => {
                window.location.href = affUrl;

                // Get the correct store page URL with hash format
                const storePageUrl = getStorePageUrlWithHash(couponId);

                window.open(storePageUrl, '_blank');

                closeSimpleModal();
            }, 800);
        }
    };

    window.copyCodeAndGoToStore = async function(code, affUrl, couponId) {
        if (!code) {
            if (affUrl && couponId) {
                trackCouponUsage(couponId, 'code');
                window.location.href = affUrl;
                const storePageUrl = getStorePageUrlWithHash(couponId);
                window.open(storePageUrl, '_blank');
                closeSimpleModal();
            }
            return;
        }
        let copySuccessful = false;
        copyTextToClipboard(code, function() {
                copySuccessful = true;
            showFeedback({message: halacoupon_ajax.strings.code_copied_redirect, type: 'success', context: 'modal'});
        }, function() {
            showFeedback({message: `📋 Please copy this code manually: ${code}`, type: 'warning', context: 'modal'});
            const codeBox = document.getElementById(`code-box-${couponId}`);
            if (codeBox) {
                codeBox.style.background = 'rgba(251, 191, 36, 0.3)';
                codeBox.style.borderColor = '#f59e0b';
                codeBox.style.animation = 'pulse 1s infinite';
                if (!document.getElementById('pulse-animation')) {
                    const style = document.createElement('style');
                    style.id = 'pulse-animation';
                    style.textContent = `@keyframes pulse {0% { transform: scale(1); }50% { transform: scale(1.05); }100% { transform: scale(1); }}`;
                    document.head.appendChild(style);
                }
            }
        });
        if (couponId) {
            trackCouponUsage(couponId, 'code');
        }
        if (affUrl) {
            const waitTime = copySuccessful ? 2000 : 4000;
            setTimeout(() => {
                showFeedback({message: 'Redirecting now... 🚀', type: 'info', context: 'modal'});
            }, waitTime - 500);
            setTimeout(() => {
                window.location.href = affUrl;
                const storePageUrl = getStorePageUrlWithHash(couponId);
                window.open(storePageUrl, '_blank');
                closeSimpleModal();
            }, waitTime);
        }
    };

    // 📋 COPY CODE TO CLIPBOARD HELPER (FOR CODE BOX CLICK)
    window.copyCodeToClipboard = function(code, couponId) {
        let copySuccessful = false;
        copyTextToClipboard(code, function() {
                    copySuccessful = true;
                    updateCodeBoxVisuals(couponId, true);
        }, function() {
                    updateCodeBoxVisuals(couponId, false);
                    showFeedback({message: 'Failed to copy code to clipboard.', type: 'error', context: 'modal'});
                });
    }

    // Helper function to update code box visuals
    function updateCodeBoxVisuals(couponId, success) {
        const indicator = document.getElementById(`copy-indicator-${couponId}`);
        const codeBox = document.getElementById(`code-box-${couponId}`);

        if (success) {
            if (indicator) {
                indicator.style.background = '#10b981';
                indicator.style.color = '#d1d5db';
                indicator.textContent = halacoupon_ajax.strings.code_copied;

                setTimeout(() => {
                    indicator.style.background = '#fbbf24';
                    indicator.style.color = '#000';
                    indicator.textContent = halacoupon_ajax.strings.copy_code;
                }, 2000);
            }

            if (codeBox) {
                codeBox.style.background = 'rgba(16, 185, 129, 0.2)';
                codeBox.style.borderColor = '#10b981';

                setTimeout(() => {
                    codeBox.style.background = '#d1d5db';
                    codeBox.style.borderColor = 'rgba(255,255,255,0.3)';
                }, 2000);
            }

            showFeedback({message: halacoupon_ajax.strings.code_copied_redirect, type: 'success', context: 'modal'});
        } else {
            if (indicator) {
                indicator.style.background = '#ef4444';
                indicator.style.color = 'white';
                indicator.textContent = '❌ FAILED';

                setTimeout(() => {
                    indicator.style.background = '#fbbf24';
                    indicator.style.color = '#92400e';
                    indicator.textContent = halacoupon_ajax.strings.copy_code;
                }, 2000);
            }

            showFeedback({message: halacoupon_ajax.strings.copy_failed, type: 'error', context: 'modal'});
        }
    }

    // 🔗 COPY SHARE URL FUNCTION
    window.copyShareUrl = function(inputId) {
        const input = document.getElementById(inputId);
        if (!input) {
            return;
        }
        const shareUrl = input.value;
        let copySuccessful = false;
        copyTextToClipboard(shareUrl, function() {
                copySuccessful = true;
                showShareFeedback(inputId, true);
        }, function() {
                showShareFeedback(inputId, false);
            });
    };

    // 🎯 SHOW SHARE FEEDBACK
    function showShareFeedback(inputId, success) {
        const input = document.getElementById(inputId);
        const button = input ? input.nextElementSibling : null;

        if (success) {
            showFeedback({message: halacoupon_ajax.strings.share_link_copied, type: 'success', context: 'modal'});

            if (button) {
                const originalText = button.textContent;

                button.textContent = halacoupon_ajax.strings.copied_success;
                button.style.background = '#10b981';

                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = '#6b7280';
                }, 2000);
            }
        } else {
            showFeedback({message: halacoupon_ajax.strings.copy_failed, type: 'error', context: 'modal'});

            if (input) {
                input.select();
                input.focus();
            }
        }
    }

    // 🎯 TRACK COUPON USAGE FUNCTION
    function trackCouponUsage(couponId, type) {
        // Check if ST object is available, use fallback if not
        const ajaxUrl = (typeof ST !== 'undefined' && ST.ajax_url) ? ST.ajax_url : '/wp-admin/admin-ajax.php';
        const couponNonce = (typeof ST !== 'undefined' && ST._coupon_nonce) ? ST._coupon_nonce : '';
        const wpNonce = (typeof ST !== 'undefined' && ST._wpnonce) ? ST._wpnonce : '';

        $.ajax({
            url: ajaxUrl,
            cache: false,
            data: {
                coupon_id: couponId,
                action: 'halacoupon_coupon_ajax',
                st_doing: 'tracking_coupon',
                _coupon_nonce: couponNonce,
                _wpnonce: wpNonce,
            },
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                // Update usage count in UI with correct selectors
                const $usageCount = $(`[data-id="${couponId}"] .font-semibold.text-gray-700`);
                if ($usageCount.length) {
                    const currentCount = parseInt($usageCount.text()) || 0;
                    $usageCount.text(currentCount + 1);
                } else {
                    // Try alternative selectors based on actual HTML structure
                    const $altUsage = $(`[data-id="${couponId}"] .text-xs .font-semibold, [data-id="${couponId}"] .count`);
                    if ($altUsage.length) {
                        const currentCount = parseInt($altUsage.text()) || 0;
                        $altUsage.text(currentCount + 1);
                    }
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.error('Track coupon usage error:', {
                    status: jqXHR.status,
                    error: errorThrown,
                    couponId: couponId,
                    type: type
                });
                showFeedback({message: 'Failed to track coupon usage. Please try again.', type: 'error', context: 'modal'});
            }
        });
    }

    // 🎯 ENHANCED SHOW FEEDBACK IN MODAL WITH TYPES
    function showModalFeedback(message, type = 'info') {
        const modal = document.getElementById('global-coupon-modal');
        if (!modal) return;

        // Define colors based on type
        const colors = {
            success: { bg: '#10b981', border: '#059669' },
            error: { bg: '#ef4444', border: '#dc2626' },
            warning: { bg: '#f59e0b', border: '#d97706' },
            info: { bg: '#3b82f6', border: '#2563eb' }
        };

        const color = colors[type] || colors.info;

        // Update feedback for screen readers
        let feedback = modal.querySelector('.modal-feedback');
        if (feedback) {
            feedback.textContent = message;
        }

        // Update visible feedback (toast/alert)
        let visibleFeedback = modal.querySelector('.modal-feedback-visible');
        if (visibleFeedback) {
            visibleFeedback.style.display = 'block';
            visibleFeedback.style.background = color.bg;
            visibleFeedback.style.color = 'white';
            visibleFeedback.style.padding = '16px 24px';
            visibleFeedback.style.borderRadius = '12px';
            visibleFeedback.style.fontSize = '14px';
            visibleFeedback.style.fontWeight = '600';
            visibleFeedback.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2)';
            visibleFeedback.style.border = `2px solid ${color.border}`;
            visibleFeedback.style.minWidth = '200px';
            visibleFeedback.style.textAlign = 'center';
            visibleFeedback.style.opacity = '1';
            visibleFeedback.style.transition = 'all 0.3s ease';
            visibleFeedback.textContent = message;

            // Auto hide after different durations based on type
            const hideDelay = type === 'warning' ? 5000 : type === 'error' ? 4000 : 3000;
            setTimeout(() => {
                visibleFeedback.style.opacity = '0';
                setTimeout(() => {
                    visibleFeedback.style.display = 'none';
                }, 300);
            }, hideDelay);
        }
    }
    
    // 🎯 URL HASH DETECTION
    function checkUrlHash() {
        const hash = window.location.hash;
        if (hash && hash.startsWith('#')) {
            const couponId = hash.substring(1);
            if (/^\d+$/.test(couponId)) {
                // For URL hash, always open modal (not direct action)
                // Try to find any element with the coupon ID
                let $element = $(`.fresh-coupon-btn[data-coupon-id="${couponId}"]`).first();

                if (!$element.length) {
                    $element = $(`a[data-type="sale"][data-coupon-id="${couponId}"]`).first();
                }

                if (!$element.length) {
                    $element = $(`a[data-coupon-id="${couponId}"][data-type]`).first();
                }

                if ($element.length) {
                    // Extract data and open modal directly (bypass click handlers)
                    const $card = $element.closest('[data-id]');
                    const couponData = extractCouponData($card, $element);

                    openSimpleModal(couponData);
                }
            }
        }
    }

    // 🎯 GET STORE PAGE URL WITH HASH FORMAT
    function getStorePageUrlWithHash(couponId) {
        // Try to find the coupon element to get the store URL
        let $element = $(`.fresh-coupon-btn[data-coupon-id="${couponId}"]`).first();

        if (!$element.length) {
            $element = $(`a[data-type="sale"][data-coupon-id="${couponId}"]`).first();
        }

        if (!$element.length) {
            $element = $(`a[data-coupon-id="${couponId}"][data-type]`).first();
        }

        if ($element.length) {
            // Get the href attribute which should contain the correct store URL with hash
            const href = $element.attr('href');
            if (href && href.includes('#')) {
                return href;
            }
        }

        // Fallback: Try to construct from current page if we're on a store page
        const currentUrl = window.location.href.split('#')[0];

        // Use global utility function to check if we're on a store page
        if (window.halacouponUtils && window.halacouponUtils.isStorePage()) {
            const storePageUrl = currentUrl + '#' + couponId;
            return storePageUrl;
        }

        // Final fallback: Use current URL (not ideal but better than nothing)
        const fallbackUrl = currentUrl + '#' + couponId;
        return fallbackUrl;
    }

    // Make the function globally available
    window.getStorePageUrlWithHash = getStorePageUrlWithHash;

    // Initialize
    setTimeout(checkUrlHash, 1000);
    $(window).on('hashchange', checkUrlHash);

    // ============================================================================
    // LIKE BUTTON FUNCTIONALITY
    // ============================================================================

    /**
     * Initialize like button functionality
     */
    function initLikeButtons() {
        // Check if halacoupon_ajax is available
        if (typeof halacoupon_ajax === 'undefined') {
            return;
        }
        
        // Use jQuery for better event delegation
        $(document).off('click.likeButton').on('click.likeButton', '.like-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const button = this;
            
            if (button.disabled) {
                return;
            }
            
            handleLikeButtonClick(button);
        });
    }

    /**
     * Handle like button click
     */
    function handleLikeButtonClick(button) {
        const couponId = button.dataset.couponId;
        const hasVoted = button.dataset.hasVoted === '1';
        
        if (hasVoted) {
            return; // User already voted
        }
        
        if (!halacoupon_ajax || !halacoupon_ajax.ajax_url || !halacoupon_ajax.nonce) {
            showNotification('Vote system not properly initialized. Please refresh the page.', 'error');
            return;
        }
        
        // Show loading state
        const originalContent = button.innerHTML;
        
        // Get the icon class from the original button
        const originalIcon = button.querySelector('svg');
        const iconClass = originalIcon ? originalIcon.className : 'w-3 h-3';
        
        button.innerHTML = `
            <svg class="${iconClass} animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
            <span class="font-bold vote-count">${button.querySelector('.vote-count')?.textContent || '0'}</span>
        `;
        button.disabled = true;
        
        // Send AJAX request
        const formData = new FormData();
        formData.append('action', 'halacoupon_coupon_ajax');
        formData.append('st_doing', 'vote_coupon');
        formData.append('coupon_id', couponId);
        formData.append('vote_type', 'up');
        formData.append('_wpnonce', halacoupon_ajax.nonce);
        
        fetch(halacoupon_ajax.ajax_url, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text().then(text => {
                try {
                    return JSON.parse(text);
                } catch (e) {
                    throw new Error('Invalid JSON response from server');
                }
            });
        })
        .then(data => {
            if (data.success) {
                // Update button to liked state with total votes
                updateLikeButtonToLiked(button, data.data.vote_count, data.data.total_votes);
                
                // Show success notification with vote statistics
                const notificationText = data.data.total_votes > 1 ? 
                    `${data.data.msg} (${data.data.vote_count}/${data.data.total_votes} votes)` :
                    data.data.msg;
                showNotification(notificationText, 'success');
                
                // Trigger custom event for other components that might need to update
                document.dispatchEvent(new CustomEvent('couponVoted', {
                    detail: {
                        couponId: couponId,
                        voteCount: data.data.vote_count,
                        totalVotes: data.data.total_votes,
                        successRate: data.data.success_rate,
                        stats: data.data.stats
                    }
                }));
                
                // Update all like buttons for this coupon across the page
                updateAllLikeButtonsForCoupon(couponId, data.data.vote_count, data.data.total_votes);
            } else {
                // Restore original state
                button.innerHTML = originalContent;
                button.disabled = false;
                showNotification(data.data.msg, 'error');
            }
        })
        .catch(error => {
            // Restore original state
            button.innerHTML = originalContent;
            button.disabled = false;
            showNotification('An error occurred while processing your vote. Please try again.', 'error');
        });
    }

    /**
     * Update like button to liked state
     */
    function updateLikeButtonToLiked(button, voteCount, totalVotes = null) {
        // First, restore the original button structure
        button.innerHTML = `
            <svg class="w-3 h-3" fill="currentColor" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"/>
            </svg>
            <span class="font-bold vote-count">${voteCount}</span>
            ${totalVotes > voteCount ? `<span class="text-xs opacity-75 total-votes">/${totalVotes}</span>` : ''}
        `;
        
        // Update button classes
        button.classList.remove(
            'bg-gradient-to-r', 'from-primary-100', 'to-accent-100', 
            'text-primary-700', 'border-primary-200', 'hover:bg-gradient-primary', 
            'hover:text-white', 'hover:shadow-medium'
        );
        button.classList.add(
            'bg-gradient-to-r', 'from-green-100', 'to-emerald-100', 
            'text-green-700', 'border-green-200', 'cursor-not-allowed', 'opacity-75'
        );
        
        // Update data attributes
        const oldHasVoted = button.dataset.hasVoted;
        const oldVoteCount = button.dataset.voteCount;
        
        button.dataset.hasVoted = '1';
        button.dataset.voteCount = voteCount;
        if (totalVotes !== null) {
            button.dataset.totalVotes = totalVotes;
        }
        button.setAttribute('aria-label', 'You have already voted for this coupon');
        button.disabled = true;

        // Add pulse animation for feedback
        button.classList.add('animate-pulse');
        setTimeout(() => button.classList.remove('animate-pulse'), 600);
    }

    /**
     * Show notification
     */
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        notification.textContent = message;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // Initialize like buttons when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        initLikeButtons();
    });

    // Also initialize for dynamically loaded content
    document.addEventListener('couponModalLoaded', function() {
        initLikeButtons();
    });

    /**
     * Update all like buttons for a specific coupon across the page
     */
    function updateAllLikeButtonsForCoupon(couponId, voteCount, totalVotes) {
        const allButtons = document.querySelectorAll(`.like-btn[data-coupon-id="${couponId}"]`);
        
        allButtons.forEach((button, index) => {
            // Only update buttons that haven't been voted on yet
            if (button.dataset.hasVoted === '0') {
                updateLikeButtonToLiked(button, voteCount, totalVotes);
            } else {
                // Update vote count for already voted buttons
                const voteCountElement = button.querySelector('.vote-count');
                if (voteCountElement) {
                    voteCountElement.textContent = voteCount;
                }
                
                // Update total votes if it exists
                const totalVotesElement = button.querySelector('.total-votes');
                if (totalVotesElement && totalVotes > voteCount) {
                    totalVotesElement.textContent = `/${totalVotes}`;
                }
                
                // Update data attributes
                button.dataset.voteCount = voteCount;
                if (totalVotes !== null) {
                    button.dataset.totalVotes = totalVotes;
                }
            }
        });
    }
});

// Utility: Copy text to clipboard (with fallback)
function copyTextToClipboard(text, onSuccess, onError) {
    // Try execCommand first (for local/older browsers)
    try {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        textArea.style.opacity = '0';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        textArea.setSelectionRange(0, 99999);
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);
        if (successful) {
            if (onSuccess) onSuccess();
            return true;
        }
    } catch (err) {}
    // Fallback: Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(() => {
            if (onSuccess) onSuccess();
        }).catch(() => {
            if (onError) onError();
        });
        return;
    }
    if (onError) onError();
}


