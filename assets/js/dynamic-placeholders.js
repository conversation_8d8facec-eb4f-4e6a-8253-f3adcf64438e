/**
 * Dynamic Placeholders System - Frontend JavaScript
 *
 * A comprehensive system for replacing placeholders/shortcodes with dynamic data on the frontend
 * Supports multisite, multilingual, and various dynamic content
 *
 * @package HalaCoupon
 * @subpackage Assets\JS
 * @version 1.0.0
 */

(function($) {
    'use strict';

    /**
     * Main Dynamic Placeholders Class
     */
    class HalaCouponDynamicPlaceholders {
        constructor() {
            this.placeholders = {};
            this.cache = {};
            this.init();
        }

        /**
         * Initialize the system
         */
        init() {
            // Register default placeholders
            this.registerDefaultPlaceholders();

            // Only process content that's specifically marked for JS processing
            $(document).ready(() => {
                this.processJSOnlyContent();
            });

            // Process content on AJAX complete (for dynamic content)
            $(document).ajaxComplete(() => {
                this.processJSOnlyContent();
            });

            // Allow manual processing
            window.halacouponProcessPlaceholders = (content) => this.processContent(content);
            window.halacouponRegisterPlaceholder = (name, callback, description) => this.registerPlaceholder(name, callback, description);
        }

        /**
         * Register a new placeholder
         */
        registerPlaceholder(name, callback, description = '') {
            this.placeholders[name] = {
                callback: callback,
                description: description
            };
        }

        /**
         * Register all default placeholders
         */
        registerDefaultPlaceholders() {
            // Essential Date & Time placeholders
            this.registerPlaceholder('year', () => new Date().getFullYear(), 'Current year');
            this.registerPlaceholder('month', () => new Date().toLocaleDateString('default', { month: 'long' }), 'Current month name');

            // Essential Site Information (from WordPress localized data)
            this.registerPlaceholder('sitetitle', () => this.getWPData('site_title', document.title), 'Site title');
            this.registerPlaceholder('sitename', () => this.getWPData('site_name', document.title), 'Site name');
            this.registerPlaceholder('siteurl', () => this.getWPData('site_url', window.location.origin), 'Site URL');
            this.registerPlaceholder('homeurl', () => this.getWPData('home_url', window.location.origin), 'Home URL');

            // Multisite Support
            this.registerPlaceholder('network_name', () => this.getWPData('network_name', this.getWPData('site_title', document.title)), 'Network name');
            this.registerPlaceholder('network_url', () => this.getWPData('network_url', window.location.origin), 'Network URL');
            this.registerPlaceholder('blog_id', () => this.getWPData('blog_id', '1'), 'Blog ID');

            // Geographic & Localization
            this.registerPlaceholder('country', () => this.getCurrentCountry(), 'Current country');
            this.registerPlaceholder('currency', () => this.getCurrentCurrency(), 'Current currency');
            this.registerPlaceholder('currency_symbol', () => this.getCurrentCurrencySymbol(), 'Currency symbol');

            // Essential Theme Data
            this.registerPlaceholder('total_coupons', () => this.getWPData('total_coupons', '0'), 'Total coupons');
            this.registerPlaceholder('total_stores', () => this.getWPData('total_stores', '0'), 'Total stores');

            // Context-aware placeholders (Essential)
            this.registerPlaceholder('store', () => this.getCurrentStore(), 'Current store name (context-aware)');
            this.registerPlaceholder('store_url', () => this.getCurrentStoreUrl(), 'Current store URL (context-aware)');
            this.registerPlaceholder('category', () => this.getCurrentCategory(), 'Current category name (context-aware)');
            this.registerPlaceholder('category_url', () => this.getCurrentCategoryUrl(), 'Current category URL (context-aware)');
            this.registerPlaceholder('coupon_title', () => this.getCurrentCouponTitle(), 'Current coupon title (in loop)');
            this.registerPlaceholder('coupon_code', () => this.getCurrentCouponCode(), 'Current coupon code (in loop)');
            this.registerPlaceholder('coupon_discount', () => this.getCurrentCouponDiscount(), 'Current coupon discount (in loop)');
        }

        /**
         * Process only JS-specific content (not SEO-critical content)
         */
        processJSOnlyContent() {
            // Only process elements specifically marked for JS processing
            // This avoids interfering with SEO-critical content that should be processed server-side

            // Process data attributes specifically marked for JS
            $('[data-js-placeholder]').each((index, element) => {
                const $element = $(element);
                const placeholder = $element.data('js-placeholder');
                const processedContent = this.processContent(placeholder);
                $element.text(processedContent);
            });

            // Process elements with .js-placeholder class
            $('.js-placeholder').each((index, element) => {
                const $element = $(element);
                const originalText = $element.text();
                const processedText = this.processContent(originalText);

                if (originalText !== processedText) {
                    $element.text(processedText);
                }
            });

            // Process HTML content specifically marked for JS
            $('.js-placeholder-html').each((index, element) => {
                const $element = $(element);
                const originalHtml = $element.html();
                const processedHtml = this.processContent(originalHtml);

                if (originalHtml !== processedHtml) {
                    $element.html(processedHtml);
                }
            });

            // Process dynamic content that changes based on user interaction
            $('.dynamic-content[data-template]').each((index, element) => {
                const $element = $(element);
                const template = $element.data('template');
                const processedContent = this.processContent(template);
                $element.html(processedContent);
            });
        }

        /**
         * Process content and replace placeholders
         */
        processContent(content) {
            if (!content || typeof content !== 'string') {
                return content;
            }

            // Find all placeholders in the format {placeholder}
            const pattern = /\{([a-zA-Z0-9_]+)\}/g;

            return content.replace(pattern, (match, placeholder) => {
                // Check cache first
                const cacheKey = `placeholder_${placeholder}`;
                if (this.cache[cacheKey] !== undefined) {
                    return this.cache[cacheKey];
                }

                // Check if placeholder is registered
                if (this.placeholders[placeholder]) {
                    const callback = this.placeholders[placeholder].callback;

                    if (typeof callback === 'function') {
                        try {
                            const result = callback();
                            // Cache the result
                            this.cache[cacheKey] = result;
                            return result;
                        } catch (error) {
                            console.warn(`Error processing placeholder ${placeholder}:`, error);
                        }
                    }
                }

                // Return original if placeholder not found
                return match;
            });
        }

        /**
         * Get WordPress data from localized script
         */
        getWPData(key, fallback = '') {
            // Try multiple possible sources
            const sources = [
                window.halacoupon_placeholders,
                window.halacoupon_ajax,
                window.wp_data,
                window.wpData
            ];

            for (const source of sources) {
                if (source && source[key] !== undefined) {
                    return source[key];
                }
            }

            return fallback;
        }

        /**
         * Geographic & Localization Functions
         */
        getCurrentCountry() {
            // Try to get from various sources
            let country = this.getWPData('country', '');
            
            if (!country) {
                // Try to detect from locale
                const locale = this.getCurrentLocale();
                const countryMap = {
                    'en-US': 'United States',
                    'en-GB': 'United Kingdom',
                    'ar-SA': 'Saudi Arabia',
                    'ar-EG': 'Egypt',
                    'ar-AE': 'United Arab Emirates',
                    'fr-FR': 'France',
                    'de-DE': 'Germany',
                    'es-ES': 'Spain',
                    'it-IT': 'Italy',
                    'ja-JP': 'Japan',
                    'zh-CN': 'China',
                    'ru-RU': 'Russia'
                };
                
                country = countryMap[locale] || 'Global';
            }
            
            return country;
        }

        getCurrentCountryCode() {
            let countryCode = this.getWPData('country_code', '');
            
            if (!countryCode) {
                const locale = this.getCurrentLocale();
                const parts = locale.split('-');
                countryCode = parts.length > 1 ? parts[1] : 'US';
            }
            
            return countryCode;
        }

        getCurrentLanguage() {
            const locale = this.getCurrentLocale();
            const languageMap = {
                'en-US': 'English',
                'en-GB': 'English',
                'ar-SA': 'العربية',
                'ar-EG': 'العربية',
                'ar-AE': 'العربية',
                'fr-FR': 'Français',
                'de-DE': 'Deutsch',
                'es-ES': 'Español',
                'it-IT': 'Italiano',
                'ja-JP': '日本語',
                'zh-CN': '中文',
                'ru-RU': 'Русский'
            };
            
            return languageMap[locale] || 'English';
        }

        getCurrentLocale() {
            return this.getWPData('locale', navigator.language || 'en-US');
        }

        getCurrentCurrency() {
            let currency = this.getWPData('currency', '');
            
            if (!currency) {
                const countryCode = this.getCurrentCountryCode();
                const currencyMap = {
                    'US': 'USD',
                    'GB': 'GBP',
                    'SA': 'SAR',
                    'AE': 'AED',
                    'EG': 'EGP',
                    'FR': 'EUR',
                    'DE': 'EUR',
                    'ES': 'EUR',
                    'IT': 'EUR',
                    'JP': 'JPY',
                    'CN': 'CNY',
                    'RU': 'RUB'
                };
                
                currency = currencyMap[countryCode] || 'USD';
            }
            
            return currency;
        }

        getCurrentCurrencySymbol() {
            let symbol = this.getWPData('currency_symbol', '');
            
            if (!symbol) {
                const currency = this.getCurrentCurrency();
                const symbolMap = {
                    'USD': '$',
                    'EUR': '€',
                    'GBP': '£',
                    'SAR': 'ر.س',
                    'AED': 'د.إ',
                    'EGP': 'ج.م',
                    'JPY': '¥',
                    'CNY': '¥',
                    'RUB': '₽'
                };
                
                symbol = symbolMap[currency] || '$';
            }
            
            return symbol;
        }

        /**
         * Browser/Device Detection Functions
         */
        getBrowserName() {
            const userAgent = navigator.userAgent;
            
            if (userAgent.includes('Chrome')) return 'Chrome';
            if (userAgent.includes('Firefox')) return 'Firefox';
            if (userAgent.includes('Safari')) return 'Safari';
            if (userAgent.includes('Edge')) return 'Edge';
            if (userAgent.includes('Opera')) return 'Opera';
            
            return 'Unknown';
        }

        getDeviceType() {
            const userAgent = navigator.userAgent;

            if (/tablet|ipad|playbook|silk/i.test(userAgent)) {
                return 'Tablet';
            }
            if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) {
                return 'Mobile';
            }

            return 'Desktop';
        }

        /**
         * Context-aware placeholder functions
         */

        /**
         * Get current store name from various contexts
         */
        getCurrentStore() {
            // Try to get from page context
            const pageStore = this.getWPData('current_store', '');
            if (pageStore) return pageStore;

            // Try to get from URL (store taxonomy page)
            const urlPath = window.location.pathname;
            const storeMatch = urlPath.match(/\/store\/([^\/]+)/);
            if (storeMatch) {
                return decodeURIComponent(storeMatch[1]).replace(/-/g, ' ');
            }

            // Try to get from body classes
            const bodyClasses = document.body.className;
            const storeClassMatch = bodyClasses.match(/tax-coupon_store-([^\s]+)/);
            if (storeClassMatch) {
                return decodeURIComponent(storeClassMatch[1]).replace(/-/g, ' ');
            }

            // Try to get from current coupon context
            const couponStore = this.getElementData('data-coupon-store');
            if (couponStore) return couponStore;

            // Try to get from global context
            if (window.halacoupon_current_store) {
                return window.halacoupon_current_store;
            }

            return '';
        }

        /**
         * Get current store URL
         */
        getCurrentStoreUrl() {
            // Try to get from page context
            const storeUrl = this.getWPData('current_store_url', '');
            if (storeUrl) return storeUrl;

            // Try to construct from current store name
            const storeName = this.getCurrentStore();
            if (storeName) {
                const slug = storeName.toLowerCase().replace(/\s+/g, '-');
                return `${window.location.origin}/store/${slug}/`;
            }

            return '';
        }

        /**
         * Get current category name
         */
        getCurrentCategory() {
            // Try to get from page context
            const pageCategory = this.getWPData('current_category', '');
            if (pageCategory) return pageCategory;

            // Try to get from URL (category taxonomy page)
            const urlPath = window.location.pathname;
            const categoryMatch = urlPath.match(/\/category\/([^\/]+)/);
            if (categoryMatch) {
                return decodeURIComponent(categoryMatch[1]).replace(/-/g, ' ');
            }

            // Try to get from body classes
            const bodyClasses = document.body.className;
            const categoryClassMatch = bodyClasses.match(/tax-coupon_category-([^\s]+)/);
            if (categoryClassMatch) {
                return decodeURIComponent(categoryClassMatch[1]).replace(/-/g, ' ');
            }

            // Try to get from current coupon context
            const couponCategory = this.getElementData('data-coupon-category');
            if (couponCategory) return couponCategory;

            return '';
        }

        /**
         * Get current coupon title
         */
        getCurrentCouponTitle() {
            // Try to get from current coupon context
            const couponTitle = this.getElementData('data-coupon-title');
            if (couponTitle) return couponTitle;

            // Try to get from page title if on single coupon
            if (document.body.classList.contains('single-coupon')) {
                return document.title.split(' - ')[0];
            }

            // Try to get from global context
            if (window.halacoupon_current_coupon_title) {
                return window.halacoupon_current_coupon_title;
            }

            return '';
        }

        /**
         * Get current coupon code
         */
        getCurrentCouponCode() {
            // Try to get from current coupon context
            const couponCode = this.getElementData('data-coupon-code');
            if (couponCode) return couponCode;

            // Try to get from code elements on page
            const codeElement = document.querySelector('.coupon-code, [data-code]');
            if (codeElement) {
                return codeElement.textContent || codeElement.getAttribute('data-code');
            }

            // Try to get from global context
            if (window.halacoupon_current_coupon_code) {
                return window.halacoupon_current_coupon_code;
            }

            return '';
        }

        /**
         * Get current category URL
         */
        getCurrentCategoryUrl() {
            // Try to get from page context
            const categoryUrl = this.getWPData('current_category_url', '');
            if (categoryUrl) return categoryUrl;

            // Try to construct from current category name
            const categoryName = this.getCurrentCategory();
            if (categoryName) {
                const slug = categoryName.toLowerCase().replace(/\s+/g, '-');
                return `${window.location.origin}/category/${slug}/`;
            }

            return '';
        }

        /**
         * Get current coupon discount
         */
        getCurrentCouponDiscount() {
            // Try to get from current coupon context
            const couponDiscount = this.getElementData('data-coupon-discount');
            if (couponDiscount) return couponDiscount;

            // Try to get from discount elements on page
            const discountElement = document.querySelector('.coupon-discount, [data-discount]');
            if (discountElement) {
                return discountElement.textContent || discountElement.getAttribute('data-discount');
            }

            // Try to get from global context
            if (window.halacoupon_current_coupon_discount) {
                return window.halacoupon_current_coupon_discount;
            }

            return '';
        }

        /**
         * Helper function to get data from current element context
         */
        getElementData(attribute) {
            // Try to find the attribute in various contexts
            const selectors = [
                `[${attribute}]`,
                '.coupon-card',
                '.store-card',
                '.current-item'
            ];

            for (const selector of selectors) {
                const element = document.querySelector(selector);
                if (element && element.getAttribute(attribute)) {
                    return element.getAttribute(attribute);
                }
            }

            return '';
        }

        /**
         * Get available placeholders for admin/debugging
         */
        getAvailablePlaceholders() {
            const list = {};
            for (const [name, data] of Object.entries(this.placeholders)) {
                list[name] = {
                    placeholder: `{${name}}`,
                    description: data.description
                };
            }
            return list;
        }

        /**
         * Clear cache
         */
        clearCache() {
            this.cache = {};
        }
    }

    // Initialize the system
    const placeholderSystem = new HalaCouponDynamicPlaceholders();

    // Make it globally accessible
    window.HalaCouponPlaceholders = placeholderSystem;

    // jQuery plugin for easy use
    $.fn.processPlaceholders = function() {
        return this.each(function() {
            const $element = $(this);
            const content = $element.is('input, textarea') ? $element.val() : $element.text();
            const processed = placeholderSystem.processContent(content);
            
            if ($element.is('input, textarea')) {
                $element.val(processed);
            } else {
                $element.text(processed);
            }
        });
    };

})(jQuery);
